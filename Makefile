.SILENT:
.PHONY: all build build-api build-cron reset-docs import-device-names pull restart release db-start db-create db-connect db-connect-admin db-destroy deploy start

BINARY_API := bin/api
BINARY_CRON := bin/cron

# Default target
all: build

# --- EVERYWHERE ------
reset-docs:
	go run cmd/reset-docs/main.go --debug

import-device-names:
	go run cmd/import-device-names/main.go --debug

# --- PRODUCTION ------
build: build-api build-cron

build-api:
	go build -o $(BINARY_API) cmd/api/main.go

build-cron:
	go build -o $(BINARY_CRON) cmd/cron/main.go

pull:
	git pull

restart:
	sudo systemctl restart appio.api
	echo "appio.api restarted"
	sudo systemctl restart appio.api.cron
	echo "appio.api.cron restarted"

release: pull build restart
	echo "🟢 Released"

# --- LOCAL ------
start: build-api
	$(BINARY_API)

start-cron: build-cron
	$(BINARY_CRON)

db-start:
	cd configs/db && docker-compose up --detach
	echo "⏳ Waiting for db"
	sleep 3
	cd configs/db && docker-compose ps --filter "status=running" appio_db

db-create: db-start

db-connect:
	PGPASSWORD="pass_api" psql -h localhost -U appio_api -d appio -p 5432

db-destroy:
	cd configs/db && docker-compose down -v --remove-orphans

deploy:
	echo "Run: ./deploy.sh \"git commit message\""

test:
	echo "Run: ./test.sh"