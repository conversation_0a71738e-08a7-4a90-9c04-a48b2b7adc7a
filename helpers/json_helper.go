package helpers

import (
	"api.appio.so/models"
	"api.appio.so/pkg"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
)

func DecodeJSON[T any](r *http.Request) (T, error) {
	var v T
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Optional: Enforce strict JSON structure
	if err := decoder.Decode(&v); err != nil {
		return v, fmt.Erro<PERSON>("decode json: %w", err)
	}
	return v, nil
}

// TODO: refactor current validators to use this
func DecodeValidateJSON[T models.Validator](r *http.Request) (T, map[string]string, error) {
	var v T
	decoder := json.NewDecoder(r.Body)
	decoder.DisallowUnknownFields() // Optional: Enforce strict JSON structure
	if err := decoder.Decode(&v); err != nil {
		return v, nil, fmt.Errorf("decode json: %w", err)
	}
	if problems := v.Valid(r.Context()); len(problems) > 0 {
		return v, problems, fmt.Errorf("invalid %T: %d problems", v, len(problems))
	}
	return v, nil, nil
}

type JSONErrorResponse struct {
	Error errorType `json:"error"`
}

type errorType struct {
	Message string         `json:"message"`
	Data    map[string]any `json:"data,omitempty"`
}

func RenderJSON(w http.ResponseWriter, r *http.Request, data any, status int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(status)

	if nil == data {
		return
	}

	// NOTE: using this method instead of json.NewEncoder(w).Encode(data) to prevent empty array being encoded as null
	b, err := json.Marshal(data)
	if err != nil {
		http.Error(w, `{"error":{"message": "render error"}}`, http.StatusInternalServerError)
	}
	if string(b) == "null" {
		b = []byte("[]")
	}
	if _, err = w.Write(b); err != nil {
		http.Error(w, `{"error":{"message": "render error"}}`, http.StatusInternalServerError)
	}
}

func RenderJSONError(w http.ResponseWriter, r *http.Request, err error) {
	status, message := pkg.MapErrorToResponse(err)

	var data map[string]any
	var ufe *pkg.UserFacingError
	if errors.As(err, &ufe) {
		data = ufe.Data()
	}

	RenderJSON(w, r, JSONErrorResponse{
		Error: errorType{
			Message: message,
			Data:    data,
		},
	}, status)
}
