package helpers

import (
	"api.appio.so/pkg"
	"bytes"
	"context"
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
)

// Test struct for DecodeJSON
type TestStruct struct {
	Name  string `json:"name"`
	Age   int    `json:"age"`
	Email string `json:"email"`
}

// Test struct that implements Validator interface
type ValidatorStruct struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

func (v ValidatorStruct) Valid(ctx context.Context) map[string]string {
	problems := make(map[string]string)
	if v.Name == "" {
		problems["name"] = "name is required"
	}
	if v.Email == "" {
		problems["email"] = "email is required"
	}
	if v.Email != "" && !strings.Contains(v.Email, "@") {
		problems["email"] = "email must be valid"
	}
	return problems
}

func TestDecodeJSON(t *testing.T) {
	t.Run("Success - valid JSON", func(t *testing.T) {
		jsonData := `{"name": "<PERSON>", "age": 30, "email": "<EMAIL>"}`
		req := httptest.NewRequest("POST", "/test", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")

		result, err := DecodeJSON[TestStruct](req)

		assert.NoError(t, err)
		assert.Equal(t, "John Doe", result.Name)
		assert.Equal(t, 30, result.Age)
		assert.Equal(t, "<EMAIL>", result.Email)
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		jsonData := `{"name": "John Doe", "age": 30, "email":}`
		req := httptest.NewRequest("POST", "/test", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")

		result, err := DecodeJSON[TestStruct](req)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "decode json")
		assert.Equal(t, TestStruct{}, result)
	})

	t.Run("RawError - unknown fields", func(t *testing.T) {
		jsonData := `{"name": "John Doe", "age": 30, "email": "<EMAIL>", "unknown": "field"}`
		req := httptest.NewRequest("POST", "/test", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")

		result, err := DecodeJSON[TestStruct](req)

		// The function uses DisallowUnknownFields, so this should error
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "decode json")
		assert.Contains(t, err.Error(), "unknown field")
		// The function returns the partially parsed data even on error
		assert.Equal(t, "John Doe", result.Name)
		assert.Equal(t, 30, result.Age)
		assert.Equal(t, "<EMAIL>", result.Email)
	})

	t.Run("Success - empty JSON object", func(t *testing.T) {
		jsonData := `{}`
		req := httptest.NewRequest("POST", "/test", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")

		result, err := DecodeJSON[TestStruct](req)

		assert.NoError(t, err)
		assert.Equal(t, "", result.Name)
		assert.Equal(t, 0, result.Age)
		assert.Equal(t, "", result.Email)
	})

	t.Run("RawError - empty body", func(t *testing.T) {
		req := httptest.NewRequest("POST", "/test", bytes.NewBufferString(""))
		req.Header.Set("Content-Type", "application/json")

		result, err := DecodeJSON[TestStruct](req)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "decode json")
		assert.Equal(t, TestStruct{}, result)
	})
}

func TestDecodeValidateJSON(t *testing.T) {
	t.Run("Success - valid JSON and validation", func(t *testing.T) {
		jsonData := `{"name": "John Doe", "email": "<EMAIL>"}`
		req := httptest.NewRequest("POST", "/test", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")

		result, problems, err := DecodeValidateJSON[ValidatorStruct](req)

		assert.NoError(t, err)
		assert.Nil(t, problems)
		assert.Equal(t, "John Doe", result.Name)
		assert.Equal(t, "<EMAIL>", result.Email)
	})

	t.Run("RawError - validation fails", func(t *testing.T) {
		jsonData := `{"name": "", "email": "invalid-email"}`
		req := httptest.NewRequest("POST", "/test", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")

		result, problems, err := DecodeValidateJSON[ValidatorStruct](req)

		assert.Error(t, err)
		assert.NotNil(t, problems)
		assert.Contains(t, problems, "name")
		assert.Contains(t, problems, "email")
		assert.Equal(t, "name is required", problems["name"])
		assert.Equal(t, "email must be valid", problems["email"])
		assert.Equal(t, "", result.Name)
		assert.Equal(t, "invalid-email", result.Email)
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		jsonData := `{"name": "John", "email":}`
		req := httptest.NewRequest("POST", "/test", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")

		result, problems, err := DecodeValidateJSON[ValidatorStruct](req)

		assert.Error(t, err)
		assert.Nil(t, problems)
		assert.Contains(t, err.Error(), "decode json")
		assert.Equal(t, ValidatorStruct{}, result)
	})

	t.Run("Partial validation errors", func(t *testing.T) {
		jsonData := `{"name": "John", "email": ""}`
		req := httptest.NewRequest("POST", "/test", bytes.NewBufferString(jsonData))
		req.Header.Set("Content-Type", "application/json")

		result, problems, err := DecodeValidateJSON[ValidatorStruct](req)

		assert.Error(t, err)
		assert.NotNil(t, problems)
		assert.Contains(t, problems, "email")
		assert.NotContains(t, problems, "name") // Name is valid
		assert.Equal(t, "John", result.Name)
		assert.Equal(t, "", result.Email)
	})
}

func TestRenderJSON(t *testing.T) {
	t.Run("Success - render object", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/test", nil)
		data := map[string]string{"message": "hello"}

		RenderJSON(w, req, data, http.StatusOK)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
		assert.Contains(t, w.Body.String(), "hello")
	})

	t.Run("Success - render nil data", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/test", nil)

		RenderJSON(w, req, nil, http.StatusNoContent)

		assert.Equal(t, http.StatusNoContent, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
		assert.Empty(t, w.Body.String())
	})

	t.Run("Success - render empty slice as []", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/test", nil)
		var data []string

		RenderJSON(w, req, data, http.StatusOK)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
		assert.Equal(t, "[]", w.Body.String())
	})

	t.Run("Success - render array", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/test", nil)
		data := []string{"item1", "item2"}

		RenderJSON(w, req, data, http.StatusOK)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
		assert.Contains(t, w.Body.String(), "item1")
		assert.Contains(t, w.Body.String(), "item2")
	})

	t.Run("Different status codes", func(t *testing.T) {
		statuses := []int{
			http.StatusOK,
			http.StatusCreated,
			http.StatusAccepted,
			http.StatusNoContent,
		}

		for _, status := range statuses {
			t.Run("Status", func(t *testing.T) {
				w := httptest.NewRecorder()
				req := httptest.NewRequest("GET", "/test", nil)
				data := map[string]string{"status": "test"}

				RenderJSON(w, req, data, status)

				assert.Equal(t, status, w.Code)
				assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
			})
		}
	})
}

func TestRenderJSONError(t *testing.T) {
	t.Run("Standard error", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/test", nil)

		RenderJSONError(w, req, pkg.ErrNotFound)

		assert.Equal(t, http.StatusNotFound, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
		assert.Contains(t, w.Body.String(), "error")
		assert.Contains(t, w.Body.String(), "message")
	})

	t.Run("UserFacingError with data", func(t *testing.T) {
		w := httptest.NewRecorder()
		req := httptest.NewRequest("GET", "/test", nil)

		data := map[string]any{
			"field": "email",
			"value": "invalid",
		}
		userErr := pkg.NewUserFacingError(pkg.ErrInvalidInput, data)

		RenderJSONError(w, req, userErr)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response JSONErrorResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.NotEmpty(t, response.Error.Message)
		assert.Equal(t, data, response.Error.Data)
	})

	t.Run("Different error types", func(t *testing.T) {
		errors := []error{
			pkg.ErrInvalidInput,
			pkg.ErrUnauthorized,
			pkg.ErrForbidden,
			pkg.ErrNotFound,
			pkg.ErrInternal,
		}

		for _, err := range errors {
			t.Run("RawError type", func(t *testing.T) {
				w := httptest.NewRecorder()
				req := httptest.NewRequest("GET", "/test", nil)

				RenderJSONError(w, req, err)

				assert.NotEqual(t, 0, w.Code)
				assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
				assert.Contains(t, w.Body.String(), "error")
			})
		}
	})
}
