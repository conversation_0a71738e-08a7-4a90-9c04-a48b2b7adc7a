package tmp

var AndroidNumberConfig = `
{
	"variants": [
		{
			"properties": {
				"version": "1.0",
				"width": 250,
				"height": 250,
				"background": "clear"
			},
			"elements": [
                {
                    "type": "column",
                    "properties": {
                        "height": "max",
                        "width": "max",
                        "horizontalAlignment": "center",
                        "verticalAlignment": "center"
                    },
                    "elements": [
                        {
                            "type": "lastUpdated",
                            "properties": {
								"width": "max",
                                "alignment": "end",
                                "color": "secondary",
                                "fontSize": 10
                            }
                        },
                        {
                            "type": "text",
                            "properties": {
                                "text": "{{VALUE}}",
                                "fontSize": 30,
                                "fontWeight": "bold",
                                "padding": {
                                    "top": 10,
                                    "bottom": 10
                                }
                            }
                        },
                        {
                            "type": "refreshButton"
                        }
                    ]
				}
			]
		}
	]
}
`

var AndroidRingConfig = `
{
	"variants": [
		{
			"properties": {
				"version": "1.0",
				"width": 250,
				"height": 250,
				"background": "clear"
			},
			"elements": [
                {
                    "type": "column",
                    "properties": {
                        "height": "max",
                        "width": "max",
                        "horizontalAlignment": "center",
                        "verticalAlignment": "center"
                    },
                    "elements": [
                        {
                            "type": "lastUpdated",
                            "properties": {
                                "width": "max",
                                "alignment": "end",
                                "color": "secondary",
                                "fontSize": 10
                            }
                        },
                        {
                            "type": "gauge",
                            "properties": {
                                "value": {{VALUE}},
                                "label": "{{VALUE}}",
                                "color": "primary",
                                "tint": "primary",
                                "fontSize": 20,
                                "size": 50,
                                "padding": {
                                    "top": 10,
                                    "bottom": 10
                                }
                            }
                        },
                        {
                            "type": "refreshButton"
                        }
                    ]
				}
			]
		}
	]
}
`

var IOSNumberConfig = `
{
	"variants": [
		{
			"properties": {
				"version": "1.0",
				"supportedFamilies": ["systemSmall", "systemMedium", "systemLarge", "systemExtraLarge"],
				"background": "clear"
			},
			"elements": [
				{
					"type": "hstack",
					"properties": {},
					"elements": [
						{
							"type": "spacer",
							"properties": {}
						},
						{
							"type": "lastUpdated",
							"properties": {
								"color": "secondary",
								"fontSize": 12,
								"padding": {
									"right": 10
								}
							}
						}
					]
				},
				{
					"type": "text",
					"properties": {
						"text": "{{VALUE}}",
						"fontSize": 40,
						"fontWeight": "semibold",
						"color": "primary",
						"padding": {
							"top": 10,
							"bottom": 10
						}
					}
				},
				{
					"type": "refreshButton",
					"properties": {
						"text": "Refresh",
						"style": "borderedProminent",
						"color": "#fff"
					}
				}
			]
		}
	]
}
`

var IOSRingConfig = `
{
	"variants": [
		{
			"properties": {
				"version": "1.0",
				"supportedFamilies": ["systemSmall", "systemMedium", "systemLarge", "systemExtraLarge"],
				"background": "clear"
			},
			"elements": [
				{
					"type": "hstack",
					"properties": {},
					"elements": [
						{
							"type": "spacer",
							"properties": {}
						},
						{
							"type": "lastUpdated",
							"properties": {
								"color": "secondary",
								"fontSize": 12,
								"padding": {
									"right": 10
								}
							}
						}
					]
				},
				{
					"type": "gauge",
					"properties": {
						"value": {{VALUE}},
						"currentValueLabel": "{{VALUE}}",
						"style": "accessoryCircularCapacity",
						"color": "primary",
						"tint": "primary",
						"padding": {
							"top": 10,
							"bottom": 10
						}
					}
				},
				{
					"type": "refreshButton",
					"properties": {
						"text": "Refresh",
						"style": "borderedProminent",
						"color": "#fff",
						"padding": {
							"bottom": 10
						}
					}
				}
			]
		}
	]
}
`

var AndroidTmpConfig = `
{
    "variants": [
        {
            "properties": {
                "version": "1.0",
                "width": 250,
                "height": 250,
                "background": "clear",
                "url": "https://appio.so"
            },
            "elements": [
                {
                    "type": "row",
                    "properties": {
                        "background": "red",
                        "width": "max",
                        "height": "max",
                        "horizontalAlignment": "start",
                        "verticalAlignment": "center"
                    },
                    "elements": [
                        {
                            "type": "column",
                            "properties": {
                                "background": "green",
                                "height": "max",
                                "horizontalAlignment": "center",
                                "verticalAlignment": "center"
                            },
                            "elements": [
                                {
                                    "type": "image",
                                    "properties": {
                                        "src": "https://picsum.photos/300?111",
                                        "width": 40
                                    }
                                },
                                {
                                    "type": "gauge",
                                    "properties": {
                                        "value": 22,
                                        "color": "#00ffaa",
                                        "label": "22%",
                                        "tint": "pink",
                                        "fontSize": 22,
                                        "fontWeight": "bold"
                                    }
                                }
                            ]
                        },
                        {
                            "type": "column",
                            "properties": {
                            },
                            "elements": [
                                {
                                    "type": "text",
                                    "properties": {
                                        "text": "Testing content 123",
                                        "fontWeight": "semibold",
                                        "color": "primary",
                                        "alignment": "center",
                                        "padding": {
                                            "top": 10,
                                            "bottom": 10
                                        }
                                    }
                                },
                                {
                                    "type": "refreshButton",
                                    "properties": {
                                        "color": "white",
                                        "tint": "yellow"
                                    }
                                },
                                {
                                    "type": "lastUpdated",
                                    "properties": {
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
}
`

var IOSTmpConfig = `
{
	"variants": [
		{
			"properties": {
				"version": "1.0",
				"supportedFamilies": ["systemSmall"]
			},
			"elements": [
				{
					"type": "text",
					"properties": {
						"text": "{{VALUE}}",
						"fontSize": 32,
						"color": "#000000,#fff"
					}
				},
				{
					"type": "refreshButton",
					"properties": {
						"text": "Refresh",
						"style": "borderedProminent",
						"color": "primary"
					}
				}
			]
		},
		{
			"properties": {
				"version": "1.0",
				"supportedFamilies": ["systemMedium"],
				"url": "https://appio.so",
				"background": "#0000FF"
			},
			"elements": [
				{
					"type": "hstack",
					"properties": {
						"spacing": 24
					},
					"elements": [
						{
							"type": "gauge",
							"properties": {
								"value": 85,
								"label": "Year of my life",
								"currentValueLabel": "85%",
								"style": "accessoryCircular",
								"color": "#ffff00",
								"tint": "#ff8800"
							}
						},
						{
							"type": "image",
							"properties": {
								"src": "https://picsum.photos/200?{{VALUE}}",
								"width": 100,
								"height": 100
							}
						},
						{
							"type": "refreshButton",
							"properties": {
								"text": "Refresh this widget",
								"style": "borderedProminent",
								"color": "#00ffff",
								"tint": "#dd3388"
							}
						}
					]
				},
				{
					"type": "hstack",
					"properties": {
						"spacing": 24
					},
					"elements": [
						{
							"type": "rectangle",
							"properties": {
								"color": "#ff0000",
								"height": 10
							}
						},
						{
							"type": "ellipse",
							"properties": {
								"color": "#ffff00",
								"width": 10,
								"height": 10
							}
						}
					]
				},
				{
					"type": "text",
					"properties": {
						"text": "Hello Widget!",
						"fontSize": 16,
						"color": "#000000",
						"background": "#00ff00"
					}
				}
			]
		}
	]
}
`
