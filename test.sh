#!/bin/sh

echo "\nRunning go tests...\n"
go test -short -count=1 ./... || { echo "\n🔴 Go tests failed 🔴\n"; exit 1; }


echo "\nRunning http tests...\n"


# test if app is running, if not build and start in background
if ! lsof -i :8082 > /dev/null 2>&1; then
  echo "Builind and starting app in the background\n"
  go build -o ./bin/test cmd/api/main.go
  ./bin/test &
  APP_PID=$!
fi


# setup. ijhttp requires java17
export JAVA_HOME=$(brew --prefix)/opt/openjdk@17/libexec/openjdk.jdk/Contents/Home

# run all http tests
for file in tests/http/*.http; do
  echo "Running test on $file"
  ijhttp -L VERBOSE "$file" || {
    echo "\n🔴 HTTP tests failed 🔴\n";
    if [ -n "$APP_PID" ]; then
      echo "Killing app with PID $APP_PID..."
      kill "$APP_PID"
    fi
    exit 1;
  }
done


if [ -n "$APP_PID" ]; then
    echo "Killing the app with PID $APP_PID..."
    kill "$APP_PID"
fi

echo "\n🟢 Tests passed 🟢\n"