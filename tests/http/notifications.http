# Setup
@host = http://localhost:8082

# TODO TESTING add tests to verify that there is no access to data across organizations without proper auth

###
# @name LIST NOTIFICATIONS
GET {{host}}/v1/notifications
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain notifications array with exact notification data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length >= 1, `Notifications array length was: ${response.body.length}`)

        // Validate each notification object in the array
        response.body.forEach((notification, index) => {
            client.assert(notification.id && notification.id.startsWith('ntf_'), `Notification ${index} ID was: ${notification.id}`)
            client.assert(notification.service_id === "svc_00000000000000000000000000", `Notification ${index} service_id was: ${notification.service_id}`)
            client.assert(['created', 'queued', 'completed'].includes(notification.status), `Notification ${index} status was: ${notification.status}`)
            client.assert(notification.payload && typeof notification.payload === 'object' && ['notification for user', 'notification for device', 'testing', 'DB init'].includes(notification.payload.message), `Notification ${index} payload was: ${JSON.stringify(notification.payload)}`)
            client.assert(typeof notification.scheduled_at === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+\+\d{2}:\d{2}$/.test(notification.scheduled_at), `Notification ${index} scheduled_at was: ${notification.scheduled_at}`)

            client.assert(!('customer_user_id' in notification), `Notification ${index} does contain customer_user_id`)
            client.assert(!('device_id' in notification), `Notification ${index} does contain device_id`)
        })
    })
%}

###
# @name LIST NOTIFICATIONS with status filter
GET {{host}}/v1/notifications?status=queued
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain queued notifications with exact data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length >= 1, `Notifications array length was: ${response.body.length}`)

        // All notifications should have the status "queued"
        response.body.forEach((notification, index) => {
            client.assert(notification.id && notification.id.startsWith('ntf_'), `Notification ${index} ID was: ${notification.id}`)
            client.assert(notification.service_id === "svc_00000000000000000000000000", `Notification ${index} service_id was: ${notification.service_id}`)
            client.assert(notification.status === "queued", `Notification ${index} status was: ${notification.status}`)
            client.assert(notification.payload && typeof notification.payload === 'object' && ['notification for user', 'notification for device', 'testing', 'DB init'].includes(notification.payload.message), `Notification ${index} payload was: ${JSON.stringify(notification.payload)}`)
            client.assert(typeof notification.scheduled_at === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+\+\d{2}:\d{2}$/.test(notification.scheduled_at), `Notification ${index} scheduled_at was: ${notification.scheduled_at}`)

            client.assert(!('customer_user_id' in notification), `Notification ${index} does contain customer_user_id`)
            client.assert(!('device_id' in notification), `Notification ${index} does contain device_id`)
        })
    })
%}

###
# @name LIST NOTIFICATIONS for device
GET {{host}}/v1/notifications?device_id=dvc_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain device notifications with exact data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length >= 1, `Notifications array length was: ${response.body.length}`)

        // All notifications should be for the specific device
        response.body.forEach((notification, index) => {
            client.assert(notification.id && notification.id.startsWith('ntf_'), `Notification ${index} ID was: ${notification.id}`)
            client.assert(notification.service_id === "svc_00000000000000000000000000", `Notification ${index} service_id was: ${notification.service_id}`)
            client.assert(['created', 'queued', 'completed'].includes(notification.status), `Notification ${index} status was: ${notification.status}`)
            client.assert(notification.payload && typeof notification.payload === 'object' && ['notification for user', 'notification for device', 'testing', 'DB init'].includes(notification.payload.message), `Notification ${index} payload was: ${JSON.stringify(notification.payload)}`)
            client.assert(typeof notification.scheduled_at === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+\+\d{2}:\d{2}$/.test(notification.scheduled_at), `Notification ${index} scheduled_at was: ${notification.scheduled_at}`)

            client.assert(!('customer_user_id' in notification), `Notification ${index} does contain customer_user_id`)
            client.assert(!('device_id' in notification), `Notification ${index} does contain device_id`)
        })
    })
%}

###
# @name LIST NOTIFICATIONS for device with status filter
GET {{host}}/v1/notifications?device_id=dvc_00000000000000000000000000&status=queued
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain queued device notifications with exact data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length >= 1, `Notifications array length was: ${response.body.length}`)

        // All notifications should be queued and for the specific device
        response.body.forEach((notification, index) => {
            client.assert(notification.id && notification.id.startsWith('ntf_'), `Notification ${index} ID was: ${notification.id}`)
            client.assert(notification.service_id === "svc_00000000000000000000000000", `Notification ${index} service_id was: ${notification.service_id}`)
            client.assert(notification.status === "queued", `Notification ${index} status was: ${notification.status}`)
            client.assert(notification.payload && typeof notification.payload === 'object' && ['notification for user', 'notification for device', 'testing', 'DB init'].includes(notification.payload.message), `Notification ${index} payload was: ${JSON.stringify(notification.payload)}`)
            client.assert(typeof notification.scheduled_at === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+\+\d{2}:\d{2}$/.test(notification.scheduled_at), `Notification ${index} scheduled_at was: ${notification.scheduled_at}`)

            client.assert(!('customer_user_id' in notification), `Notification ${index} does contain customer_user_id`)
            client.assert(!('device_id' in notification), `Notification ${index} does contain device_id`)
        })
    })
%}

###
# @name LIST NOTIFICATIONS for user
GET {{host}}/v1/notifications?user_id=ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain user notifications with exact data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length >= 1, `Notifications array length was: ${response.body.length}`)

        // All notifications should be for the specific user
        response.body.forEach((notification, index) => {
            client.assert(notification.id && notification.id.startsWith('ntf_'), `Notification ${index} ID was: ${notification.id}`)
            client.assert(notification.service_id === "svc_00000000000000000000000000", `Notification ${index} service_id was: ${notification.service_id}`)
            client.assert(['created', 'queued', 'completed'].includes(notification.status), `Notification ${index} status was: ${notification.status}`)
            client.assert(notification.payload && typeof notification.payload === 'object' && ['notification for user', 'notification for device', 'testing', 'DB init'].includes(notification.payload.message), `Notification ${index} payload was: ${JSON.stringify(notification.payload)}`)
            client.assert(typeof notification.scheduled_at === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+\+\d{2}:\d{2}$/.test(notification.scheduled_at), `Notification ${index} scheduled_at was: ${notification.scheduled_at}`)

            client.assert(!('customer_user_id' in notification), `Notification ${index} does contain customer_user_id`)
            client.assert(!('device_id' in notification), `Notification ${index} does contain device_id`)
        })
    })
%}

###
# @name LIST NOTIFICATIONS for user with status filter
GET {{host}}/v1/notifications?user_id=ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a&status=queued
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain queued user notifications with exact data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.length >= 1, `Notifications array length was: ${response.body.length}`)

        // All notifications should be queued and for the specific user
        response.body.forEach((notification, index) => {
            client.assert(notification.id && notification.id.startsWith('ntf_'), `Notification ${index} ID was: ${notification.id}`)
            client.assert(notification.service_id === "svc_00000000000000000000000000", `Notification ${index} service_id was: ${notification.service_id}`)
            client.assert(notification.status === "queued", `Notification ${index} status was: ${notification.status}`)
            client.assert(notification.payload && typeof notification.payload === 'object' && ['notification for user', 'notification for device', 'testing', 'DB init'].includes(notification.payload.message), `Notification ${index} payload was: ${JSON.stringify(notification.payload)}`)
            client.assert(typeof notification.scheduled_at === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+\+\d{2}:\d{2}$/.test(notification.scheduled_at), `Notification ${index} scheduled_at was: ${notification.scheduled_at}`)

            client.assert(!('customer_user_id' in notification), `Notification ${index} does contain customer_user_id`)
            client.assert(!('device_id' in notification), `Notification ${index} does contain device_id`)
        })
    })
%}

###
# @name LIST NOTIFICATIONS as demo
GET {{host}}/demo-appio-so/notifications
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain demo notifications with exact data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)

        // Validate each notification object in the array
        response.body.forEach((notification, index) => {
            client.assert(notification.id && notification.id.startsWith('ntf_'), `Notification ${index} ID was: ${notification.id}`)
            client.assert(notification.service_id === "demo_svc_00000000000000000000000000", `Notification ${index} service_id was: ${notification.service_id}`)
            client.assert(['created', 'queued', 'completed'].includes(notification.status), `Notification ${index} status was: ${notification.status}`)
            client.assert(notification.payload && typeof notification.payload === 'object' && ['notification for user', 'notification for device', 'testing', 'DB init'].includes(notification.payload.message), `Notification ${index} payload was: ${JSON.stringify(notification.payload)}`)
            client.assert(typeof notification.scheduled_at === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+\+\d{2}:\d{2}$/.test(notification.scheduled_at), `Notification ${index} scheduled_at was: ${notification.scheduled_at}`)

            client.assert(!('customer_user_id' in notification), `Notification ${index} does contain customer_user_id`)
            client.assert(!('device_id' in notification), `Notification ${index} does contain device_id`)
        })
    })
%}

###
# @name LIST NOTIFICATIONS as ios
GET {{host}}/mobile/notifications
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-App-Platform: ios
X-Service-Id: svc_00000000000000000000000000
X-Device-Id: dvc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain iOS notifications with exact data", () => {
        client.assert(Array.isArray(response.body), `Response body was: ${JSON.stringify(response.body)}`)

        // Validate each notification object in the array
        response.body.forEach((notification, index) => {
            client.assert(notification.id && notification.id.startsWith('ntf_'), `Notification ${index} ID was: ${notification.id}`)
            client.assert(notification.service_id === "svc_00000000000000000000000000", `Notification ${index} service_id was: ${notification.service_id}`)
            client.assert(['created', 'queued', 'completed'].includes(notification.status), `Notification ${index} status was: ${notification.status}`)
            client.assert(notification.payload && typeof notification.payload === 'object' && ['notification for user', 'notification for device', 'testing', 'DB init'].includes(notification.payload.message), `Notification ${index} payload was: ${JSON.stringify(notification.payload)}`)
            client.assert(typeof notification.scheduled_at === 'string' && /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d+\+\d{2}:\d{2}$/.test(notification.scheduled_at), `Notification ${index} scheduled_at was: ${notification.scheduled_at}`)

            client.assert(!('customer_user_id' in notification), `Notification ${index} does contain customer_user_id`)
            client.assert(!('device_id' in notification), `Notification ${index} does contain device_id`)
        })
    })
%}

###
# @name CREATE NOTIFICATION
POST {{host}}/v1/notifications
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

{
  "payload": {"message":  "testing"}
}

> {%
    client.global.set("notification_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact notification ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('ntf_'), `Notification ID was: ${response.body.id}`)
    })
%}

###
# @name CREATE NOTIFICATION scheduled in 10 minutes
< {%
    request.variables.set("scheduled_at", (new Date(Date.now() + (10 * 60 * 1000))).toISOString())
%}

POST {{host}}/v1/notifications
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

{
  "payload": {"message":  "testing"},
  "scheduled_at": "{{scheduled_at}}"
}

> {%
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact scheduled notification ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('ntf_'), `Notification ID was: ${response.body.id}`)
    })
%}

###
# @name CREATE NOTIFICATION invalid scheduled in 31 days
< {%
    request.variables.set("scheduled_at", (new Date(Date.now() + (31 * 24 * 60 * 60 * 1000))).toISOString())
%}

POST {{host}}/v1/notifications
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

{
  "payload": {"message":  "testing"},
  "scheduled_at": "{{scheduled_at}}"
}

> {%
    client.test("Response status should be 400", () => {
        client.assert(response.status === 400, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Invalid input data", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name CREATE NOTIFICATION scheduled with invalid date/time
< {%
    request.variables.set("scheduled_at", "invalid")
%}

POST {{host}}/v1/notifications
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

{
  "payload": {"message":  "testing"},
  "scheduled_at": "{{scheduled_at}}"
}

> {%
    client.test("Response status should be 400", () => {
        client.assert(response.status === 400, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Invalid input data", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name CREATE NOTIFICATION for device
POST {{host}}/v1/notifications?device_id=dvc_00000000000000000000000000
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

{
  "payload": {"message":  "notification for device"}
}

> {%
    client.global.set("device_notification_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact device notification ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('ntf_'), `Notification ID was: ${response.body.id}`)
    })
%}

###
# @name CREATE NOTIFICATION for user
POST {{host}}/v1/notifications?user_id=ac640c1edfc5e446968dece24f7f458fdbab89c8f533d96cbc545517c51bdf1a
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

{
  "payload": {"message":  "notification for user"}
}

> {%
    client.global.set("user_notification_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact user notification ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('ntf_'), `Notification ID was: ${response.body.id}`)
    })
%}

###
# @name CREATE NOTIFICATION as demo.appio.so
POST {{host}}/demo-appio-so/notifications
Authorization: Bearer dev_11111111111111111111111111111111111111111111111111
X-Service-Id: demo_svc_00000000000000000000000000

{
  "payload": {"message":  "testing"}
}

> {%
    client.global.set("demo_notification_id", response.body.id);
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact demo notification ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('ntf_'), `Notification ID was: ${response.body.id}`)
    })
%}

###
# @name GET NOTIFICATION
GET {{host}}/v1/notifications/{{notification_id}}
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact notification data", () => {
        client.assert(response.body.id && response.body.id.startsWith('ntf_'), `Notification ID was: ${response.body.id}`)
        client.assert(response.body.service_id === "svc_00000000000000000000000000", `Service ID was: ${response.body.service_id}`)
        client.assert(['created', 'queued', 'completed'].includes(response.body.status), `Status was: ${response.body.status}`)
        client.assert(response.body.payload && response.body.payload.message === "testing", `Payload was: ${JSON.stringify(response.body.payload)}`)
        client.assert(typeof response.body.scheduled_at === 'string', `Scheduled at was: ${response.body.scheduled_at}`)
        client.assert(typeof response.body.delivery_stats === 'object', `Delivery stats was: ${JSON.stringify(response.body.delivery_stats)}`)
        client.assert(typeof response.body.delivery_stats.total === 'number', `Delivery stats total was: ${response.body.delivery_stats.total}`)
        client.assert(typeof response.body.delivery_stats.created === 'number', `Delivery stats created was: ${response.body.delivery_stats.total}`)
        client.assert(typeof response.body.delivery_stats.queued === 'number', `Delivery stats queued was: ${response.body.delivery_stats.total}`)
        client.assert(typeof response.body.delivery_stats.completed === 'number', `Delivery stats completed was: ${response.body.delivery_stats.total}`)
        client.assert(typeof response.body.delivery_stats.failed === 'number', `Delivery stats failed was: ${response.body.delivery_stats.total}`)

        client.assert(!('customer_user_id' in response.body), `Response does contain customer_user_id`)
        client.assert(!('device_id' in response.body), `Response does contain device_id`)
    })
%}

###
# @name GET NOTIFICATION - not found
GET {{host}}/v1/notifications/ntf_00000000xxxxxxxxxxxxxxxxxx
Authorization: Bearer dev_99999999999999999999999999999999999999999999999999
X-Service-Id: svc_00000000000000000000000000

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}

###
## @ name MARK NOTIFICATION DELIVERED as ios - not queued automatically therefore will return 404
#PATCH {{host}}/mobile/notifications/{{notification_id}}/delivered
#Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
#X-App-Platform: ios
#X-Service-Id: svc_00000000000000000000000000
#X-Device-Id: dvc_00000000000000000000000000
#
#> {%
#    client.test("Response status should be 200", () => {
#        client.assert(response.status === 200, `Response status was: ${response.status}`)
#    })
#%}

###
# @name MARK NOTIFICATION DELIVERED as ios - not found
PATCH {{host}}/mobile/notifications/ntf_00000000xxxxxxxxxxxxxxxxxx/delivered
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-App-Platform: ios
X-Service-Id: svc_00000000000000000000000000
X-Device-Id: dvc_00000000000000000000000000

> {%
    client.test("Response status should be 404", () => {
        client.assert(response.status === 404, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain exact error message", () => {
        client.assert(response.body.error && response.body.error.message === "Resource not found", `Error message was: ${response.body.error?.message}`)
    })
%}

###
# @name DELETE NOTIFICATION
# n/a

###
