# Setup
@host = http://localhost:8082

###
# @name CREATE FEEDBACK without service and device as android
POST {{host}}/mobile/feedback
Authorization: Bearer dev_44444444444444444444444444444444444444444444444444
X-App-Platform: android
X-App-Version: 1.0

{
  "message": "test feedback without service and without device"
}

> {%
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain feedback ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('fdb_'), `Response body was: ${JSON.stringify(response.body)}`)
    })
%}

###
# @name CREATE FEEDBACK as android
POST {{host}}/mobile/feedback
Authorization: Bearer dev_44444444444444444444444444444444444444444444444444
X-App-Platform: android
X-App-Version: 1.0
X-Service-Id: svc_00000000000000000000000000
X-Device-Id: dvc_00000000000000000000000000

{
  "message": "test feedback with service and device"
}

> {%
    client.test("Response status should be 201", () => {
        client.assert(response.status === 201, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain feedback ID", () => {
        client.assert(response.body.id && response.body.id.startsWith('fdb_'), `Response body was: ${JSON.stringify(response.body)}`)
    })
%}
