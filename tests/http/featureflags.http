# Setup
@host = http://localhost:8082

###
# @name LIST FEATURE FLAGS as ios
GET {{host}}/mobile/ff
Authorization: Bearer dev_22222222222222222222222222222222222222222222222222
X-App-Version: 1.1
X-App-Platform: ios

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain feature flag data", () => {
        client.assert(response.body.id && response.body.id.startsWith('ff_'), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.platform === 'ios', `Platform was: ${response.body.platform}`)
        client.assert(response.body.version === '1.1', `Version was: ${response.body.version}`)
        client.assert(response.body.config === '{"intro":"A"}', `Config was: ${response.body.config}`)
    })
%}

###
# @name LIST FEATURE FLAGS as android
GET {{host}}/mobile/ff
Authorization: Bearer dev_44444444444444444444444444444444444444444444444444
X-App-Version: 1.0
X-App-Platform: android

> {%
    client.test("Response status should be 200", () => {
        client.assert(response.status === 200, `Response status was: ${response.status}`)
    })

    client.test("Response content type should be application/json", () => {
        client.assert(response.contentType.mimeType === "application/json", `Content type was: ${response.contentType.mimeType}`)
    })

    client.test("Response body should contain feature flag data", () => {
        client.assert(response.body.id && response.body.id.startsWith('ff_'), `Response body was: ${JSON.stringify(response.body)}`)
        client.assert(response.body.platform === 'android', `Platform was: ${response.body.platform}`)
        client.assert(response.body.version === '1.0', `Version was: ${response.body.version}`)
        client.assert(response.body.config === '{"intro":"B"}', `Config was: ${response.body.config}`)
    })
%}