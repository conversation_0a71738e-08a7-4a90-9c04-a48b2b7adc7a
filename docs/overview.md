# Appio

https://appio.so/
Appio is the missing API for mobile features.
Appio helps businesses add mobile features (like notifications and widgets) without the need to build or maintain an app, or deal with app store approvals. Appio saves costs and makes more money by increasing customer upsales.

# api.appio.so

This project provides a REST API for the Appio platform.
It is used by multiple parties:
- customers represented as `roles.ApiUser`
- Demo app represented as `roles.DemoAppioSo`. This app is used to showcase Appio's features to potential customers. It uses private communication with the API.
- App app represented as `roles.AppApp`. Public application for our customers' users who use it to connect to Appio's mobile application.
- Appio iOS mobile app represented as `roles.IOS`. This app is used by customers' users to benefit from Appio's features.
