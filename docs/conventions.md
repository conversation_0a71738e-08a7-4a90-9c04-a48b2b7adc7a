# Error handling
- Repositories return wrapped errors. `fmt.Errorf("listing widgets: %w", err)`
- Services log errors and translate them to domain specific errors defined in `pkg.errors.go`
- Handlers and Middlewares output domain specific errors based on mapping in `pkg.errors.go`

# Requests flow
- Middlewares interact with request via context
- Handlers collect inputs and pass them into services
- Services validates inputs and pass them into repositories
- Repositories interact with database



