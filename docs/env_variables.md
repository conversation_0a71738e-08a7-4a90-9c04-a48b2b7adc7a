# Environment Variables

This document describes the environment variables used by the API and their validation rules.

## Configuration Loading

The application loads configuration from:
1. **TOML files** in the `configs/` directory (default: `config.toml`)
2. **Environment variables** that override TOML values (via Viper's `AutomaticEnv()`)

Environment variables follow the pattern: `SECTION_FIELD` (e.g., `SERVER_PORT`, `DB_SOURCE`)

## Validation Rules

All configuration is validated at startup. The application will **fail to start** if any validation rules are violated.

### Server Configuration

| Variable | Required | Validation | Example |
|----------|----------|------------|---------|
| `SERVER_NAME` | ✅ | Non-empty string | `api.appio.so` |
| `SERVER_ENV` | ✅ | One of: `dev`, `staging`, `prod`, `test` | `prod` |
| `SERVER_PORT` | ✅ | Integer between 1-65535 | `8082` |
| `SERVER_LOG_LEVEL` | ✅ | One of: `debug`, `info`, `warn`, `error`, `fatal` | `info` |
| `SERVER_TIMEOUT` | ✅ | Valid Go duration string | `30s` |

### Database Configuration

| Variable | Required | Validation | Example |
|----------|----------|------------|---------|
| `DB_SOURCE` | ✅ | Valid PostgreSQL URL with username, host, database | `postgres://user:pass@localhost:5432/db` |
| `DB_SOURCE_FING` | ✅ | Valid PostgreSQL URL for fingerprint database | `postgres://user:pass@localhost:5432/fing` |
| `DB_LOG_QUERIES` | ❌ | Boolean | `false` |

### Authentication Configuration

| Variable | Required | Validation | Example |
|----------|----------|------------|---------|
| `AUTH_APP` | ✅ | String ≥50 characters, unique | `app_key_...` |
| `AUTH_DEMO` | ✅ | String ≥50 characters, unique | `demo_key_...` |
| `AUTH_IOS` | ✅ | String ≥50 characters, unique | `ios_key_...` |

**Important**: All auth keys must be unique (no duplicates allowed).

### Optional Configuration

| Variable | Required | Validation | Example |
|----------|----------|------------|---------|
| `FINGERPRINT_LOG` | ❌ | Boolean | `false` |
| `SENTRY_DSN` | ❌ | String | `https://...` |
| `SENTRY_SEND_DEFAULT_PII` | ❌ | Boolean | `true` |
| `SENTRY_TRACE_SAMPLE_RATE` | ❌ | Float 0.0-1.0 | `1.0` |

## Error Examples

### Invalid Environment
```bash
SERVER_ENV=invalid
# Error: server environment must be one of: dev, staging, prod, test, got: invalid
```

### Invalid Database URL
```bash
DB_SOURCE=mysql://user:pass@localhost/db
# Error: main database URL must use postgres:// or postgresql:// scheme, got: mysql
```

### Short Auth Key
```bash
AUTH_APP=short
# Error: auth app key must be at least 50 characters long, got: 5
```

### Duplicate Auth Keys
```bash
AUTH_APP=same_key_12345678901234567890123456789012345678901234567890
AUTH_DEMO=same_key_12345678901234567890123456789012345678901234567890
# Error: all auth keys must be unique
```

## Production Deployment

For production deployment, ensure:

1. **All required environment variables are set**
2. **Database URLs point to production databases**
3. **Auth keys are properly generated and unique**
4. **Server environment is set to `prod`**
5. **Log level is appropriate for production (`error` or `warn`)**

## Testing Configuration

To test configuration validation:

```bash
# Run config tests
go test ./pkg/config -v

# Test with invalid config (should fail)
echo 'invalid config' > configs/test.toml
go run cmd/api/main.go  # Should fail with validation error
```

## Environment Variable Override Examples

```bash
# Override server port
export SERVER_PORT=9000

# Override database URL
export DB_SOURCE="*******************************************/appio_prod"

# Override log level
export SERVER_LOG_LEVEL=error

# Start application (will use environment overrides)
./bin/api
```

The validation ensures that:
- ✅ **Fail fast**: Invalid config causes startup failure, not runtime errors
- ✅ **Clear errors**: Descriptive error messages for debugging
- ✅ **Security**: Auth keys must be properly formatted and unique
- ✅ **Reliability**: Database URLs are validated before connection attempts
