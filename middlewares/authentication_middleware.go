package middlewares

import (
	"context"
	"net/http"
	"strings"

	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"api.appio.so/services"
)

func AuthenticationMiddleware(apiKeyService *services.APIKeyService, jwtService services.JWTServiceInterface) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			auth := r.Header.Get("Authorization")
			if auth == "" {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			token := strings.TrimPrefix(auth, "Bearer ")
			if token == "" {
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			// TODO: detect JWT and use jwtService instead apiKeyService

			ctx := r.Context()
			userID, svcID, role, err := apiKeyService.GetByAPIKey(ctx, token)
			if err != nil {
				helpers.RenderJSONError(w, r, err)
				return
			}
			ctx = context.WithValue(ctx, UsrIDKey{}, userID)
			ctx = context.WithValue(ctx, AuthSvcIDKey{}, svcID)
			ctx = context.WithValue(ctx, RoleKey{}, role)

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
