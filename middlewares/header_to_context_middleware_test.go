package middlewares

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"

	"api.appio.so/models"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
)

func TestHeaderToContextMiddleware(t *testing.T) {
	logger := zaptest.NewLogger(t)

	t.Run("ID valid header", func(t *testing.T) {
		middleware := HeaderToContextMiddleware("X-Service-Id", SvcIDKey{}, appioid.Parse, logger)

		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			serviceID, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
			assert.True(t, ok)
			assert.Equal(t, "svc_00000000000000000000000000", serviceID.String())
			w.WriteHeader(http.StatusOK)
		})

		handler := middleware(contextCheckHandler)
		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Service-Id", "svc_00000000000000000000000000")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("ID invalid header returns 400", func(t *testing.T) {
		middleware := HeaderToContextMiddleware("X-Service-Id", SvcIDKey{}, appioid.Parse, logger)

		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			t.Error("Handler should not be called when middleware returns 400")
		})

		handler := middleware(contextCheckHandler)
		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Service-Id", "invalid-id")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "Invalid X-Service-Id header")
	})

	t.Run("ID  missing header does not set context", func(t *testing.T) {
		middleware := HeaderToContextMiddleware("X-Service-Id", SvcIDKey{}, appioid.Parse, logger)

		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			_, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
			assert.False(t, ok) // Should not be set
			w.WriteHeader(http.StatusOK)
		})

		handler := middleware(contextCheckHandler)
		req := httptest.NewRequest("GET", "/test", nil)
		// No X-Service-Id header
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Custom type with string parser", func(t *testing.T) {
		// Test with a simple string parser
		stringParser := func(s string) (string, error) {
			if s == "" {
				return "", assert.AnError
			}
			return s, nil
		}

		middleware := HeaderToContextMiddleware("X-Custom-Header", "custom-key", stringParser, logger)

		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			value, ok := ctx.Value("custom-key").(string)
			assert.True(t, ok)
			assert.Equal(t, "test-value", value)
			w.WriteHeader(http.StatusOK)
		})

		handler := middleware(contextCheckHandler)
		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Custom-Header", "test-value")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Custom type with int parser - returns 400", func(t *testing.T) {
		// Test with an int parser
		intParser := func(s string) (int, error) {
			return strconv.Atoi(s)
		}

		middleware := HeaderToContextMiddleware("X-Number", "number-key", intParser, logger)

		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			t.Error("Handler should not be called when middleware returns 400")
		})

		handler := middleware(contextCheckHandler)
		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("X-Number", "not-a-number")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
		assert.Contains(t, w.Body.String(), "Invalid X-Number header")
	})

	t.Run("Context value overwrite detection", func(t *testing.T) {
		t.Run("String type - same value allows overwrite", func(t *testing.T) {
			stringParser := func(s string) (string, error) {
				return s, nil
			}

			middleware := HeaderToContextMiddleware("X-Custom-Header", "custom-key", stringParser, logger)

			contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				ctx := r.Context()
				value, ok := ctx.Value("custom-key").(string)
				assert.True(t, ok)
				assert.Equal(t, "test-value", value)
				w.WriteHeader(http.StatusOK)
			})

			handler := middleware(contextCheckHandler)
			req := httptest.NewRequest("GET", "/test", nil)

			// Pre-set the same value in context
			ctx := context.WithValue(req.Context(), "custom-key", "test-value")
			req = req.WithContext(ctx)

			req.Header.Set("X-Custom-Header", "test-value")
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
		})

		t.Run("String type - different value returns 400", func(t *testing.T) {
			stringParser := func(s string) (string, error) {
				return s, nil
			}

			middleware := HeaderToContextMiddleware("X-Custom-Header", "custom-key", stringParser, logger)

			contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				t.Error("Handler should not be called when middleware returns 400")
			})

			handler := middleware(contextCheckHandler)
			req := httptest.NewRequest("GET", "/test", nil)

			// Pre-set different value in context
			ctx := context.WithValue(req.Context(), "custom-key", "existing-value")
			req = req.WithContext(ctx)

			req.Header.Set("X-Custom-Header", "new-value")
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			assert.Equal(t, http.StatusBadRequest, w.Code)
			assert.Contains(t, w.Body.String(), "Invalid X-Custom-Header header overwrite")
		})

		t.Run("Int type - same value allows overwrite", func(t *testing.T) {
			intParser := func(s string) (int, error) {
				return strconv.Atoi(s)
			}

			middleware := HeaderToContextMiddleware("X-Number", "number-key", intParser, logger)

			contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				ctx := r.Context()
				value, ok := ctx.Value("number-key").(int)
				assert.True(t, ok)
				assert.Equal(t, 42, value)
				w.WriteHeader(http.StatusOK)
			})

			handler := middleware(contextCheckHandler)
			req := httptest.NewRequest("GET", "/test", nil)

			// Pre-set the same value in context
			ctx := context.WithValue(req.Context(), "number-key", 42)
			req = req.WithContext(ctx)

			req.Header.Set("X-Number", "42")
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
		})

		t.Run("Int type - different value returns 400", func(t *testing.T) {
			intParser := func(s string) (int, error) {
				return strconv.Atoi(s)
			}

			middleware := HeaderToContextMiddleware("X-Number", "number-key", intParser, logger)

			contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				t.Error("Handler should not be called when middleware returns 400")
			})

			handler := middleware(contextCheckHandler)
			req := httptest.NewRequest("GET", "/test", nil)

			// Pre-set different value in context
			ctx := context.WithValue(req.Context(), "number-key", 100)
			req = req.WithContext(ctx)

			req.Header.Set("X-Number", "42")
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			assert.Equal(t, http.StatusBadRequest, w.Code)
			assert.Contains(t, w.Body.String(), "Invalid X-Number header overwrite")
		})

		t.Run("Platform type - same value allows overwrite", func(t *testing.T) {
			middleware := HeaderToContextMiddleware("X-Platform", PlatformKey{}, models.ParsePlatform, logger)

			contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				ctx := r.Context()
				platform, ok := ctx.Value(PlatformKey{}).(models.Platform)
				assert.True(t, ok)
				assert.Equal(t, models.PlatformIOS, platform)
				w.WriteHeader(http.StatusOK)
			})

			handler := middleware(contextCheckHandler)
			req := httptest.NewRequest("GET", "/test", nil)

			// Pre-set the same value in context
			ctx := context.WithValue(req.Context(), PlatformKey{}, models.PlatformIOS)
			req = req.WithContext(ctx)

			req.Header.Set("X-Platform", "ios")
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
		})

		t.Run("Platform type - different value returns 400", func(t *testing.T) {
			middleware := HeaderToContextMiddleware("X-Platform", PlatformKey{}, models.ParsePlatform, logger)

			contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				t.Error("Handler should not be called when middleware returns 400")
			})

			handler := middleware(contextCheckHandler)
			req := httptest.NewRequest("GET", "/test", nil)

			// Pre-set different value in context
			ctx := context.WithValue(req.Context(), PlatformKey{}, models.PlatformAndroid)
			req = req.WithContext(ctx)

			req.Header.Set("X-Platform", "ios")
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			assert.Equal(t, http.StatusBadRequest, w.Code)
			assert.Contains(t, w.Body.String(), "Invalid X-Platform header overwrite")
		})

		t.Run("appioid.ID type - same value allows overwrite", func(t *testing.T) {
			middleware := HeaderToContextMiddleware("X-Service-Id", SvcIDKey{}, appioid.Parse, logger)

			contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				ctx := r.Context()
				serviceID, ok := ctx.Value(SvcIDKey{}).(*appioid.ID)
				assert.True(t, ok)
				assert.Equal(t, "svc_00000000000000000000000001", serviceID.String())
				w.WriteHeader(http.StatusOK)
			})

			handler := middleware(contextCheckHandler)
			req := httptest.NewRequest("GET", "/test", nil)

			// Pre-set the same value in context
			existingID := appioid.MustParse("svc_00000000000000000000000001")
			ctx := context.WithValue(req.Context(), SvcIDKey{}, existingID)
			req = req.WithContext(ctx)

			req.Header.Set("X-Service-Id", "svc_00000000000000000000000001")
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
		})

		t.Run("appioid.ID type - different value returns 400", func(t *testing.T) {
			middleware := HeaderToContextMiddleware("X-Service-Id", SvcIDKey{}, appioid.Parse, logger)

			contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				t.Error("Handler should not be called when middleware returns 400")
			})

			handler := middleware(contextCheckHandler)
			req := httptest.NewRequest("GET", "/test", nil)

			// Pre-set different value in context
			existingID := appioid.MustParse("svc_00000000000000000000000001")
			ctx := context.WithValue(req.Context(), SvcIDKey{}, existingID)
			req = req.WithContext(ctx)

			req.Header.Set("X-Service-Id", "svc_00000000000000000000000002")
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			assert.Equal(t, http.StatusBadRequest, w.Code)
			assert.Contains(t, w.Body.String(), "Invalid X-Service-Id header overwrite")
		})
	})
}

func TestValuesEqual(t *testing.T) {
	t.Run("String comparison", func(t *testing.T) {
		assert.True(t, valuesEqual("test", "test"))
		assert.False(t, valuesEqual("test", "different"))
		assert.True(t, valuesEqual("", ""))
	})

	t.Run("Int comparison", func(t *testing.T) {
		assert.True(t, valuesEqual(42, 42))
		assert.False(t, valuesEqual(42, 100))
		assert.True(t, valuesEqual(0, 0))
		assert.False(t, valuesEqual(-1, 1))
	})

	t.Run("Platform comparison", func(t *testing.T) {
		assert.True(t, valuesEqual(models.PlatformIOS, models.PlatformIOS))
		assert.False(t, valuesEqual(models.PlatformIOS, models.PlatformAndroid))
		assert.True(t, valuesEqual(models.Platform(""), models.Platform("")))
	})

	t.Run("appioid.ID pointer comparison", func(t *testing.T) {
		id1 := appioid.MustParse("svc_00000000000000000000000001")
		id2 := appioid.MustParse("svc_00000000000000000000000001")
		id3 := appioid.MustParse("svc_00000000000000000000000002")

		// Same IDs should be equal
		assert.True(t, valuesEqual(id1, id2))
		// Different IDs should not be equal
		assert.False(t, valuesEqual(id1, id3))

		// Nil comparisons
		var nilID *appioid.ID
		assert.True(t, valuesEqual(nilID, nilID))
		assert.False(t, valuesEqual(id1, nilID))
		assert.False(t, valuesEqual(nilID, id1))
	})

	t.Run("appioid.ID value comparison", func(t *testing.T) {
		id1 := *appioid.MustParse("svc_00000000000000000000000001")
		id2 := *appioid.MustParse("svc_00000000000000000000000001")
		id3 := *appioid.MustParse("svc_00000000000000000000000002")

		// Same IDs should be equal
		assert.True(t, valuesEqual(id1, id2))
		// Different IDs should not be equal
		assert.False(t, valuesEqual(id1, id3))
	})

	t.Run("Fallback to reflection for unknown types", func(t *testing.T) {
		type CustomType struct {
			Value string
		}

		custom1 := CustomType{Value: "test"}
		custom2 := CustomType{Value: "test"}
		custom3 := CustomType{Value: "different"}

		// Should use reflection for unknown types
		assert.True(t, valuesEqual(custom1, custom2))
		assert.False(t, valuesEqual(custom1, custom3))
	})
}
