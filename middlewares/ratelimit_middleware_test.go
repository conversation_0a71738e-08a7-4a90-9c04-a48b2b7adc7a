package middlewares

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
	"golang.org/x/time/rate"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestNewIPRateLimiter(t *testing.T) {
	t.Run("Creates rate limiter with correct parameters", func(t *testing.T) {
		limiter := NewIPRateLimiter(rate.Limit(10), 5)

		assert.NotNil(t, limiter)
		assert.Equal(t, rate.Limit(10), limiter.rate)
		assert.Equal(t, 5, limiter.burst)
		assert.NotNil(t, limiter.ips)
		assert.Equal(t, 0, len(limiter.ips))
	})

	t.Run("Creates rate limiter with zero values", func(t *testing.T) {
		limiter := NewIPRateLimiter(rate.Limit(0), 0)

		assert.NotNil(t, limiter)
		assert.Equal(t, rate.Limit(0), limiter.rate)
		assert.Equal(t, 0, limiter.burst)
	})
}

func TestIPRateLimiter_GetLimiter(t *testing.T) {
	rateLimiter := NewIPRateLimiter(rate.Limit(10), 5)

	t.Run("Creates new limiter for new IP", func(t *testing.T) {
		ip := "***********"
		limiter := rateLimiter.getLimiter(ip)

		assert.NotNil(t, limiter)
		assert.Equal(t, 1, len(rateLimiter.ips))
		assert.Contains(t, rateLimiter.ips, ip)
	})

	t.Run("Returns existing limiter for known IP", func(t *testing.T) {
		ip := "***********"
		limiter1 := rateLimiter.getLimiter(ip)
		limiter2 := rateLimiter.getLimiter(ip)

		assert.Equal(t, limiter1, limiter2)
		assert.Equal(t, 2, len(rateLimiter.ips)) // Should still be 2 (previous test + this one)
	})

	t.Run("Creates different limiters for different IPs", func(t *testing.T) {
		ip1 := "***********"
		ip2 := "***********"

		limiter1 := rateLimiter.getLimiter(ip1)
		limiter2 := rateLimiter.getLimiter(ip2)

		assert.NotSame(t, limiter1, limiter2) // Use NotSame instead of NotEqual for pointer comparison
		assert.Contains(t, rateLimiter.ips, ip1)
		assert.Contains(t, rateLimiter.ips, ip2)
	})

	t.Run("Thread safety", func(t *testing.T) {
		// Test concurrent access
		done := make(chan bool, 10)
		ip := "***********00"

		for i := 0; i < 10; i++ {
			go func() {
				limiter := rateLimiter.getLimiter(ip)
				assert.NotNil(t, limiter)
				done <- true
			}()
		}

		// Wait for all goroutines to complete
		for i := 0; i < 10; i++ {
			<-done
		}

		// Should only have one limiter for this IP
		assert.Contains(t, rateLimiter.ips, ip)
	})
}

func TestRateLimit(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Mock handler that should be called when rate limit is not exceeded
	mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	t.Run("Success - request within rate limit", func(t *testing.T) {
		middleware := RateLimit(logger, "prod", 100, 60, 10)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.RemoteAddr = "***********:12345"
		req.Header.Set("X-Forwarded-For", "***********")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - dev environment skips rate limiting for localhost", func(t *testing.T) {
		middleware := RateLimit(logger, "dev", 1, 60, 1) // Very restrictive limits
		handler := middleware(mockHandler)

		// Request without proper IP (simulating localhost)
		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - test environment skips rate limiting for localhost", func(t *testing.T) {
		middleware := RateLimit(logger, "test", 1, 60, 1) // Very restrictive limits
		handler := middleware(mockHandler)

		// Request without proper IP (simulating localhost)
		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("RawError - rate limit exceeded", func(t *testing.T) {
		// Very restrictive rate limit: 1 request per 60 seconds, burst of 1
		middleware := RateLimit(logger, "prod", 1, 60, 1)
		handler := middleware(mockHandler)

		ip := "***********0"

		// First request should succeed
		req1 := httptest.NewRequest("GET", "/test", nil)
		req1.RemoteAddr = ip + ":12345"
		req1.Header.Set("X-Forwarded-For", ip)
		w1 := httptest.NewRecorder()

		handler.ServeHTTP(w1, req1)
		assert.Equal(t, http.StatusOK, w1.Code)

		// Second request should be rate limited
		req2 := httptest.NewRequest("GET", "/test", nil)
		req2.RemoteAddr = ip + ":12346"
		req2.Header.Set("X-Forwarded-For", ip)
		w2 := httptest.NewRecorder()

		handler.ServeHTTP(w2, req2)
		assert.Equal(t, http.StatusTooManyRequests, w2.Code)

		// Check for Retry-After header
		retryAfter := w2.Header().Get("Retry-After")
		assert.Equal(t, "600", retryAfter)

		var response map[string]interface{}
		err := json.Unmarshal(w2.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Different IPs have separate rate limits", func(t *testing.T) {
		middleware := RateLimit(logger, "prod", 1, 60, 1)
		handler := middleware(mockHandler)

		ip1 := "***********0"
		ip2 := "***********1"

		// First IP - first request should succeed
		req1 := httptest.NewRequest("GET", "/test", nil)
		req1.RemoteAddr = ip1 + ":12345"
		req1.Header.Set("X-Forwarded-For", ip1)
		w1 := httptest.NewRecorder()

		handler.ServeHTTP(w1, req1)
		assert.Equal(t, http.StatusOK, w1.Code)

		// Second IP - first request should also succeed
		req2 := httptest.NewRequest("GET", "/test", nil)
		req2.RemoteAddr = ip2 + ":12345"
		req2.Header.Set("X-Forwarded-For", ip2)
		w2 := httptest.NewRecorder()

		handler.ServeHTTP(w2, req2)
		assert.Equal(t, http.StatusOK, w2.Code)

		// First IP - second request should be rate limited
		req3 := httptest.NewRequest("GET", "/test", nil)
		req3.RemoteAddr = ip1 + ":12346"
		req3.Header.Set("X-Forwarded-For", ip1)
		w3 := httptest.NewRecorder()

		handler.ServeHTTP(w3, req3)
		assert.Equal(t, http.StatusTooManyRequests, w3.Code)

		// Second IP - second request should also be rate limited
		req4 := httptest.NewRequest("GET", "/test", nil)
		req4.RemoteAddr = ip2 + ":12346"
		req4.Header.Set("X-Forwarded-For", ip2)
		w4 := httptest.NewRecorder()

		handler.ServeHTTP(w4, req4)
		assert.Equal(t, http.StatusTooManyRequests, w4.Code)
	})

	t.Run("Burst allows multiple requests", func(t *testing.T) {
		// Allow 1 request per second with burst of 3
		middleware := RateLimit(logger, "prod", 1, 1, 3)
		handler := middleware(mockHandler)

		ip := "***********0"

		// First 3 requests should succeed due to burst
		for i := 0; i < 3; i++ {
			req := httptest.NewRequest("GET", "/test", nil)
			req.RemoteAddr = ip + ":1234" + string(rune('0'+i))
			req.Header.Set("X-Forwarded-For", ip)
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)
			assert.Equal(t, http.StatusOK, w.Code, "Request %d should succeed", i+1)
		}

		// 4th request should be rate limited
		req4 := httptest.NewRequest("GET", "/test", nil)
		req4.RemoteAddr = ip + ":12343"
		req4.Header.Set("X-Forwarded-For", ip)
		w4 := httptest.NewRecorder()

		handler.ServeHTTP(w4, req4)
		assert.Equal(t, http.StatusTooManyRequests, w4.Code)
	})

	t.Run("Different rate limit configurations", func(t *testing.T) {
		testCases := []struct {
			name     string
			requests int
			seconds  int
			burst    int
		}{
			{"Low rate", 10, 60, 5},
			{"High rate", 1000, 60, 100},
			{"Very high rate", 10000, 3600, 1000},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				middleware := RateLimit(logger, "prod", tc.requests, tc.seconds, tc.burst)
				handler := middleware(mockHandler)

				// Should be able to make burst number of requests
				ip := "***********0"
				for i := 0; i < tc.burst; i++ {
					req := httptest.NewRequest("GET", "/test", nil)
					req.RemoteAddr = ip + ":1234" + string(rune('0'+i))
					req.Header.Set("X-Forwarded-For", ip)
					w := httptest.NewRecorder()

					handler.ServeHTTP(w, req)
					assert.Equal(t, http.StatusOK, w.Code, "Request %d should succeed for %s", i+1, tc.name)
				}
			})
		}
	})

	t.Run("IPv6 addresses", func(t *testing.T) {
		middleware := RateLimit(logger, "prod", 1, 60, 1)
		handler := middleware(mockHandler)

		// IPv6 address
		req := httptest.NewRequest("GET", "/test", nil)
		req.RemoteAddr = "[2001:db8::1]:12345"
		req.Header.Set("X-Forwarded-For", "2001:db8::1")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Malformed IP addresses", func(t *testing.T) {
		middleware := RateLimit(logger, "prod", 100, 60, 10)
		handler := middleware(mockHandler)

		testCases := []string{
			"invalid-ip:12345",
			"192.168.1:12345",
			"***********56:12345",
			":12345",
			"***********:",
		}

		for _, remoteAddr := range testCases {
			t.Run("RemoteAddr: "+remoteAddr, func(t *testing.T) {
				req := httptest.NewRequest("GET", "/test", nil)
				req.RemoteAddr = remoteAddr
				w := httptest.NewRecorder()

				// Should not panic and should handle gracefully
				handler.ServeHTTP(w, req)
				// The behavior depends on how clientip.FromRequest handles malformed addresses
				// but it shouldn't cause a panic
			})
		}
	})
}
