package middlewares

import (
	"net/http"

	"api.appio.so/services"
	"go.uber.org/zap"
)

// LastSeenAtMiddleware updates the device's last_seen_at field when a device ID is present in the context
func LastSeenAtMiddleware(lastSeenAtService services.LastSeenAtServiceInterface, logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			dvcID, ok := GetDeviceIDFromContext(ctx)
			if ok && dvcID != nil {
				// Don't fail the request if this update fails
				_ = lastSeenAtService.Update(ctx, dvcID)
			}

			next.ServeHTTP(w, r)
		})
	}
}
