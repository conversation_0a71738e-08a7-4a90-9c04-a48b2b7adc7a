package middlewares

import (
	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"context"
	"go.uber.org/zap"
	"net/http"
	"time"
)

func TimeoutMiddleware(timeout string, logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			duration, err := time.ParseDuration(timeout)
			if err != nil {
				logger.Error("parsing timeout", zap.String("timeout", timeout), zap.Error(err))
				helpers.RenderJSONError(w, r, pkg.ErrInternal)
				return
			}

			ctx, cancel := context.WithTimeout(r.Context(), duration)
			defer cancel()

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}
