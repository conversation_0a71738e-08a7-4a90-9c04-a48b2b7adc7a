package middlewares

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func TestValidateServiceUser(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	logger := zaptest.NewLogger(t)
	userID := appioid.MustParse("usr_00000000000000000000000001")
	serviceID := appioid.MustParse("svc_00000000000000000000000001")
	authServiceID := appioid.MustParse("svc_00000000000000000000000001")

	// Mock handler that should only be called if middleware passes
	mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.<PERSON>rite<PERSON>ead<PERSON>(http.StatusOK)
		w.Write([]byte("success"))
	})

	t.Run("Success - AuthSvcIDKey matches SvcIDKey", func(t *testing.T) {
		mockUserService := mocks.NewMockUserServiceInterface(ctrl)
		middleware := ValidateServiceUser(mockUserService, logger)

		ctx := context.Background()
		ctx = context.WithValue(ctx, SvcIDKey{}, serviceID)
		ctx = context.WithValue(ctx, AuthSvcIDKey{}, authServiceID)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler := middleware(mockHandler)
		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Error - AuthSvcIDKey does not match SvcIDKey", func(t *testing.T) {
		mockUserService := mocks.NewMockUserServiceInterface(ctrl)
		middleware := ValidateServiceUser(mockUserService, logger)

		differentAuthServiceID := appioid.MustParse("svc_00000000000000000000000002")
		ctx := context.Background()
		ctx = context.WithValue(ctx, SvcIDKey{}, serviceID)
		ctx = context.WithValue(ctx, AuthSvcIDKey{}, differentAuthServiceID)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler := middleware(mockHandler)
		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})

	t.Run("Success - No AuthSvcIDKey, user has access to service", func(t *testing.T) {
		mockUserService := mocks.NewMockUserServiceInterface(ctrl)
		middleware := ValidateServiceUser(mockUserService, logger)

		mockUserService.EXPECT().
			ValidateUserServiceAccess(gomock.Any(), userID, serviceID).
			Return(nil).
			Times(1)

		ctx := context.Background()
		ctx = context.WithValue(ctx, SvcIDKey{}, serviceID)
		ctx = context.WithValue(ctx, UsrIDKey{}, userID)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler := middleware(mockHandler)
		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Error - No AuthSvcIDKey, user does not have access to service", func(t *testing.T) {
		mockUserService := mocks.NewMockUserServiceInterface(ctrl)
		middleware := ValidateServiceUser(mockUserService, logger)

		mockUserService.EXPECT().
			ValidateUserServiceAccess(gomock.Any(), userID, serviceID).
			Return(pkg.ErrForbidden).
			Times(1)

		ctx := context.Background()
		ctx = context.WithValue(ctx, SvcIDKey{}, serviceID)
		ctx = context.WithValue(ctx, UsrIDKey{}, userID)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler := middleware(mockHandler)
		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusForbidden, w.Code)
	})

	t.Run("Error - No SvcIDKey in context", func(t *testing.T) {
		mockUserService := mocks.NewMockUserServiceInterface(ctrl)
		middleware := ValidateServiceUser(mockUserService, logger)

		ctx := context.Background()

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler := middleware(mockHandler)
		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Error - No UsrIDKey when AuthSvcIDKey not set", func(t *testing.T) {
		mockUserService := mocks.NewMockUserServiceInterface(ctrl)
		middleware := ValidateServiceUser(mockUserService, logger)

		ctx := context.Background()
		ctx = context.WithValue(ctx, SvcIDKey{}, serviceID)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler := middleware(mockHandler)
		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	t.Run("Error - UserService returns internal error", func(t *testing.T) {
		mockUserService := mocks.NewMockUserServiceInterface(ctrl)
		middleware := ValidateServiceUser(mockUserService, logger)

		mockUserService.EXPECT().
			ValidateUserServiceAccess(gomock.Any(), userID, serviceID).
			Return(pkg.ErrInternal).
			Times(1)

		ctx := context.Background()
		ctx = context.WithValue(ctx, SvcIDKey{}, serviceID)
		ctx = context.WithValue(ctx, UsrIDKey{}, userID)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler := middleware(mockHandler)
		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("Success - Nil AuthSvcIDKey, user has access", func(t *testing.T) {
		mockUserService := mocks.NewMockUserServiceInterface(ctrl)
		middleware := ValidateServiceUser(mockUserService, logger)

		mockUserService.EXPECT().
			ValidateUserServiceAccess(gomock.Any(), userID, serviceID).
			Return(nil).
			Times(1)

		ctx := context.Background()
		ctx = context.WithValue(ctx, SvcIDKey{}, serviceID)
		ctx = context.WithValue(ctx, AuthSvcIDKey{}, (*appioid.ID)(nil))
		ctx = context.WithValue(ctx, UsrIDKey{}, userID)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler := middleware(mockHandler)
		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})
}
