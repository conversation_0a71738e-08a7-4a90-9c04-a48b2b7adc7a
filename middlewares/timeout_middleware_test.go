package middlewares

import (
	"encoding/json"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"
)

func TestTimeoutMiddleware(t *testing.T) {
	logger := zaptest.NewLogger(t)

	t.Run("Success - request completes within timeout", func(t *testing.T) {
		// Handler that completes quickly
		fastHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("success"))
		})

		middleware := TimeoutMiddleware("5s", logger)
		handler := middleware(fastHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - context has timeout set", func(t *testing.T) {
		// Handler that checks if context has timeout
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			deadline, ok := ctx.Deadline()
			assert.True(t, ok, "Context should have a deadline")
			assert.True(t, deadline.After(time.Now()), "Deadline should be in the future")
			w.WriteHeader(http.StatusOK)
		})

		middleware := TimeoutMiddleware("10s", logger)
		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("RawError - invalid timeout format", func(t *testing.T) {
		mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		middleware := TimeoutMiddleware("invalid-timeout", logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Different timeout formats", func(t *testing.T) {
		testCases := []struct {
			name           string
			timeout        string
			shouldSucceed  bool
			expectedStatus int
		}{
			{"Seconds", "30s", true, http.StatusOK},
			{"Minutes", "5m", true, http.StatusOK},
			{"Hours", "1h", true, http.StatusOK},
			{"Milliseconds", "500ms", true, http.StatusOK},
			{"Microseconds", "100µs", true, http.StatusOK},
			{"Nanoseconds", "1000ns", true, http.StatusOK},
			{"Combined", "1h30m45s", true, http.StatusOK},
			{"Zero", "0s", true, http.StatusOK},
			{"Invalid format", "30", false, http.StatusInternalServerError},
			{"Invalid unit", "30x", false, http.StatusInternalServerError},
			{"Empty string", "", false, http.StatusInternalServerError},
			{"Just text", "timeout", false, http.StatusInternalServerError},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
					w.WriteHeader(http.StatusOK)
					w.Write([]byte("success"))
				})

				middleware := TimeoutMiddleware(tc.timeout, logger)
				handler := middleware(mockHandler)

				req := httptest.NewRequest("GET", "/test", nil)
				w := httptest.NewRecorder()

				handler.ServeHTTP(w, req)

				assert.Equal(t, tc.expectedStatus, w.Code)

				if tc.shouldSucceed {
					assert.Equal(t, "success", w.Body.String())
				} else {
					var response map[string]interface{}
					err := json.Unmarshal(w.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.Contains(t, response, "error")
				}
			})
		}
	})

	t.Run("Context cancellation", func(t *testing.T) {
		// Handler that checks if context can be cancelled
		cancellationHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			// Create a channel to simulate work
			done := make(chan bool)

			go func() {
				// Simulate some work
				time.Sleep(10 * time.Millisecond)
				done <- true
			}()

			select {
			case <-done:
				w.WriteHeader(http.StatusOK)
				w.Write([]byte("completed"))
			case <-ctx.Done():
				// Context was cancelled
				w.WriteHeader(http.StatusRequestTimeout)
				w.Write([]byte("cancelled"))
			}
		})

		middleware := TimeoutMiddleware("1s", logger)
		handler := middleware(cancellationHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// Should complete normally since 10ms < 1s
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "completed", w.Body.String())
	})

	t.Run("Very short timeout", func(t *testing.T) {
		// Handler that takes longer than the timeout
		slowHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			select {
			case <-time.After(100 * time.Millisecond):
				w.WriteHeader(http.StatusOK)
				w.Write([]byte("completed"))
			case <-ctx.Done():
				// Context was cancelled due to timeout
				return
			}
		})

		middleware := TimeoutMiddleware("1ms", logger)
		handler := middleware(slowHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// The response might be empty if the context was cancelled before the handler could write
		// This is expected behavior for timeout scenarios
		assert.True(t, w.Code == http.StatusOK || w.Code == 0)
	})

	t.Run("Timeout inheritance", func(t *testing.T) {
		// Handler that checks if the timeout is properly set
		timeoutCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			deadline, ok := ctx.Deadline()
			assert.True(t, ok)

			// Check that the deadline is approximately 2 seconds from now
			expectedDeadline := time.Now().Add(2 * time.Second)
			timeDiff := deadline.Sub(expectedDeadline)
			assert.True(t, timeDiff < 100*time.Millisecond && timeDiff > -100*time.Millisecond,
				"Deadline should be approximately 2 seconds from now")

			w.WriteHeader(http.StatusOK)
		})

		middleware := TimeoutMiddleware("2s", logger)
		handler := middleware(timeoutCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Multiple timeout middlewares", func(t *testing.T) {
		// Handler that checks the final timeout
		finalHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			deadline, ok := ctx.Deadline()
			assert.True(t, ok)

			// The inner timeout (1s) should take precedence over the outer timeout (5s)
			expectedDeadline := time.Now().Add(1 * time.Second)
			timeDiff := deadline.Sub(expectedDeadline)
			assert.True(t, timeDiff < 100*time.Millisecond && timeDiff > -100*time.Millisecond,
				"Inner timeout should take precedence")

			w.WriteHeader(http.StatusOK)
		})

		outerMiddleware := TimeoutMiddleware("5s", logger)
		innerMiddleware := TimeoutMiddleware("1s", logger)
		handler := outerMiddleware(innerMiddleware(finalHandler))

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("Negative timeout", func(t *testing.T) {
		mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		})

		middleware := TimeoutMiddleware("-5s", logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// Negative timeout should still be parsed successfully by time.ParseDuration
		// The behavior with negative timeout is implementation-dependent
		// but it shouldn't cause an error in parsing
		assert.Equal(t, http.StatusOK, w.Code)
	})
}
