package middlewares

import (
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg/config"
	"api.appio.so/pkg/roles"
	"api.appio.so/services"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func TestAuthenticationMiddleware(t *testing.T) {
	logger := zaptest.NewLogger(t)

	authConfig := &config.AuthConfig{
		App:     "app-key-12345678901234567890123456789012345678901234567890",
		IOS:     "ios-key-12345678901234567890123456789012345678901234567890",
		Android: "android-key-12345678901234567890123456789012345678901234567890",
		Demo:    "demo-key-12345678901234567890123456789012345678901234567890",
	}

	// Mock handler that should be called when authentication succeeds
	mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	t.Run("Success - valid API key with user and service", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		userID := appioid.MustParse("usr_00000000000000000000000001")
		serviceID := appioid.MustParse("svc_00000000000000000000000001")
		apiKey := "valid-api-key-12345678901234567890123456789012345678901234567890"

		mockRepo.EXPECT().
			IsActiveAPIKey(gomock.Any(), apiKey).
			Return(true)
		mockRepo.EXPECT().
			FindBy(gomock.Any(), apiKey).
			Return(userID, serviceID, nil)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+apiKey)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - valid internal app key", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+authConfig.App)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - valid iOS key", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+authConfig.IOS)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - valid Android key", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+authConfig.Android)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Error - missing Authorization header", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Error - empty Authorization header", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Error - missing Bearer token", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer ")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Error - invalid API key format", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer short-key")
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Error - blocked API key (too many attempts)", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)

		// Create a service with a very low max failures for testing
		customConfig := services.APIKeyServiceConfig{
			MaxFailures:      1,
			MaxBlockDuration: 24 * 60 * 60 * 1000000000, // 24 hours in nanoseconds
			MinKeyLength:     50,
			MaxKeyLength:     200,
			CleanupInterval:  10 * 60 * 1000000000,  // 10 minutes in nanoseconds
			AttemptRetention: 24 * 60 * 60 * 1000000000, // 24 hours in nanoseconds
		}
		testAPIKeyService := services.NewAPIKeyServiceWithConfig(mockRepo, authConfig, logger, customConfig)

		testMiddleware := AuthenticationMiddleware(testAPIKeyService, mockJWTService)
		testHandler := testMiddleware(mockHandler)

		apiKey := "blocked-key-12345678901234567890123456789012345678901234567890"

		// First, make the key fail to trigger blocking
		mockRepo.EXPECT().
			IsActiveAPIKey(gomock.Any(), apiKey).
			Return(false).
			Times(1)

		req1 := httptest.NewRequest("GET", "/test", nil)
		req1.Header.Set("Authorization", "Bearer "+apiKey)
		w1 := httptest.NewRecorder()

		testHandler.ServeHTTP(w1, req1)
		assert.Equal(t, http.StatusUnauthorized, w1.Code)

		// Second request should be blocked (no mock call expected as it's blocked before reaching the repo)
		req2 := httptest.NewRequest("GET", "/test", nil)
		req2.Header.Set("Authorization", "Bearer "+apiKey)
		w2 := httptest.NewRecorder()

		testHandler.ServeHTTP(w2, req2)

		assert.Equal(t, http.StatusTooManyRequests, w2.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w2.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Error - inactive API key", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		apiKey := "inactive-key-12345678901234567890123456789012345678901234567890"

		mockRepo.EXPECT().
			IsActiveAPIKey(gomock.Any(), apiKey).
			Return(false)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+apiKey)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Error - repository FindBy error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		apiKey := "error-key-12345678901234567890123456789012345678901234567890"

		mockRepo.EXPECT().
			IsActiveAPIKey(gomock.Any(), apiKey).
			Return(true)
		mockRepo.EXPECT().
			FindBy(gomock.Any(), apiKey).
			Return(nil, nil, errors.New("database error"))

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+apiKey)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Success - demo API key", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		// Demo API key format: demo_ + anything + last 26 chars should be valid service ID
		// The last 26 chars will be used to create "demo_svc_" + last26chars
		// Valid appio ID format: [a-z_]{2,10}_[0-9a-hjkmnp-tv-z]{26}
		demoKey := "demo_some_long_prefix_to_make_it_valid_00000000000000000000000001"

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+demoKey)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Error - invalid demo API key format", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		// Invalid demo key - too short
		demoKey := "demo_short"

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+demoKey)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Error - demo API key with invalid service ID", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
		handler := middleware(mockHandler)

		// Demo key with invalid service ID format (last 26 chars don't form valid ID)
		demoKey := "demo_some_long_prefix_to_make_it_valid_invalid_service_id_format"

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+demoKey)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Context values are set correctly", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)

		// Create a handler that checks context values
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			// Check that context values are set
			userID, ok := GetUserIDFromContext(ctx)
			assert.True(t, ok)
			assert.NotNil(t, userID)
			assert.Equal(t, "usr_00000000000000000000000001", userID.String())

			authSvcID, ok := GetAuthServiceIDFromContext(ctx)
			assert.True(t, ok)
			assert.NotNil(t, authSvcID)
			assert.Equal(t, "svc_00000000000000000000000001", authSvcID.String())

			role, ok := GetRoleFromContext(ctx)
			assert.True(t, ok)
			assert.Equal(t, roles.Api, role)

			w.WriteHeader(http.StatusOK)
			w.Write([]byte("context-check-success"))
		})

		handler := middleware(contextCheckHandler)

		userID := appioid.MustParse("usr_00000000000000000000000001")
		serviceID := appioid.MustParse("svc_00000000000000000000000001")
		apiKey := "context-test-key-12345678901234567890123456789012345678901234567890"

		mockRepo.EXPECT().
			IsActiveAPIKey(gomock.Any(), apiKey).
			Return(true)
		mockRepo.EXPECT().
			FindBy(gomock.Any(), apiKey).
			Return(userID, serviceID, nil)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+apiKey)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "context-check-success", w.Body.String())
	})

	t.Run("Context values for internal keys", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
		mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
		apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

		middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)

		// Create a handler that checks context values for internal keys
		contextCheckHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			// For internal keys, userID and authSvcID should be nil
			userID, ok := GetUserIDFromContext(ctx)
			assert.True(t, ok)
			assert.Nil(t, userID)

			authSvcID, ok := GetAuthServiceIDFromContext(ctx)
			assert.True(t, ok)
			assert.Nil(t, authSvcID)

			role, ok := GetRoleFromContext(ctx)
			assert.True(t, ok)
			assert.Equal(t, roles.IOS, role)

			w.WriteHeader(http.StatusOK)
			w.Write([]byte("internal-context-check-success"))
		})

		handler := middleware(contextCheckHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		req.Header.Set("Authorization", "Bearer "+authConfig.IOS)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "internal-context-check-success", w.Body.String())
	})

	t.Run("Different authorization header formats", func(t *testing.T) {
		testCases := []struct {
			name           string
			authHeader     string
			expectedStatus int
		}{
			{"Valid Bearer token", "Bearer " + authConfig.App, http.StatusOK},
			{"Empty after Bearer", "Bearer ", http.StatusUnauthorized}, // Empty token
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				ctrl := gomock.NewController(t)
				defer ctrl.Finish()

				mockRepo := mocks.NewMockAPIKeyRepositoryInterface(ctrl)
				mockJWTService := mocks.NewMockJWTServiceInterface(ctrl)
				apiKeyService := services.NewAPIKeyService(mockRepo, authConfig, logger)

				middleware := AuthenticationMiddleware(apiKeyService, mockJWTService)
				handler := middleware(mockHandler)

				req := httptest.NewRequest("GET", "/test", nil)
				req.Header.Set("Authorization", tc.authHeader)
				w := httptest.NewRecorder()

				handler.ServeHTTP(w, req)

				assert.Equal(t, tc.expectedStatus, w.Code)
			})
		}
	})
}
