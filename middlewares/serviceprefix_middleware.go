package middlewares

import (
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"go.uber.org/zap"
)

func ValidateServiceIDPrefix(prefix string, logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			svcID, ok := GetServiceIDFromContext(ctx)

			if ok && svcID.Type() != prefix {
				logger.Error("service prefix mismatch", zap.String("service_id", svcID.String()), zap.String("type", svcID.Type()), zap.String("prefix", prefix))
				helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}
