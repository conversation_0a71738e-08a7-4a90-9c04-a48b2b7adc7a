package middlewares

import (
	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"github.com/appio-so/go-clientip"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
	"net/http"
	"sync"
)

type IPRateLimiter struct {
	ips   map[string]*rate.Limiter
	mu    sync.RWMutex
	rate  rate.Limit
	burst int
}

func NewIPRateLimiter(r rate.Limit, b int) *IPRateLimiter {
	return &IPRateLimiter{
		ips:   make(map[string]*rate.Limiter),
		rate:  r,
		burst: b,
	}
}

func (i *IPRateLimiter) getLimiter(ip string) *rate.Limiter {
	i.mu.Lock()
	defer i.mu.Unlock()

	limiter, exists := i.ips[ip]
	if !exists {
		limiter = rate.NewLimiter(i.rate, i.burst)
		i.ips[ip] = limiter
	}

	return limiter
}

func RateLimit(logger *zap.Logger, env string, requests, perSeconds, burst int) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		limiter := NewIPRateLimiter(rate.Limit(requests/perSeconds), burst)

		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ip := clientip.FromRequest(r)

			// Skip localhost in dev
			if nil == ip && (env == "dev" || env == "test") {
				next.ServeHTTP(w, r)
				return
			}

			if !limiter.getLimiter(ip.String()).Allow() {
				logger.Warn("rate limit exceeded", zap.String("ip", ip.String()))
				w.Header().Set("Retry-After", "600") // 10 minutes
				helpers.RenderJSONError(w, r, pkg.ErrTooManyAttempts)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}
