package middlewares

import (
	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"api.appio.so/pkg/roles"
	"go.uber.org/zap"
	"net/http"
)

func RoleMiddleware(logger *zap.Logger, requiredRoles ...roles.Role) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			role, ok := GetRoleFromContext(ctx)
			if !ok {
				logger.Debug("no role in context")
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			if !hasAccess(role, requiredRoles) {
				logger.Info("access denied", zap.String("role", string(role)), zap.Any("required_roles", requiredRoles))
				helpers.RenderJSONError(w, r, pkg.ErrForbidden)
				return
			}

			next.ServeHT<PERSON>(w, r)
		})
	}
}

func hasAccess(role roles.Role, requiredRoles []roles.Role) bool {
	for _, requiredRole := range requiredRoles {
		if role == requiredRole {
			return true
		}
	}
	return false
}
