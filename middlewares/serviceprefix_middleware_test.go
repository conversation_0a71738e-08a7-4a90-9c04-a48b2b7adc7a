package middlewares

import (
	"context"
	"encoding/json"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestValidateServiceIDPrefix(t *testing.T) {
	logger := zaptest.NewLogger(t)

	// Mock handler that should be called when validation passes
	mockHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("success"))
	})

	t.Run("Success - service ID has correct prefix", func(t *testing.T) {
		svcID := appioid.MustParse("demo_svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)

		middleware := ValidateServiceIDPrefix("demo_svc", logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Success - no service ID in context", func(t *testing.T) {
		middleware := ValidateServiceIDPrefix("demo_svc", logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// When no service ID is in context, middleware should pass through
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("RawError - service ID has wrong prefix", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001") // Regular service ID
		ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)

		middleware := ValidateServiceIDPrefix("demo_svc", logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Different prefix validations", func(t *testing.T) {
		testCases := []struct {
			name           string
			serviceID      string
			requiredPrefix string
			shouldPass     bool
		}{
			{"Demo service with demo prefix", "demo_svc_00000000000000000000000001", "demo_svc", true},
			{"Regular service with svc prefix", "svc_00000000000000000000000001", "svc", true},
			{"Device with dvc prefix", "dvc_00000000000000000000000001", "dvc", true},
			{"Demo service with wrong prefix", "demo_svc_00000000000000000000000001", "svc", false},
			{"Regular service with demo prefix", "svc_00000000000000000000000001", "demo_svc", false},
			{"Custom prefix match", "custom_00000000000000000000000001", "custom", true},
			{"Custom prefix mismatch", "custom_00000000000000000000000001", "other", false},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				svcID := appioid.MustParse(tc.serviceID)
				ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)

				middleware := ValidateServiceIDPrefix(tc.requiredPrefix, logger)
				handler := middleware(mockHandler)

				req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
				w := httptest.NewRecorder()

				handler.ServeHTTP(w, req)

				if tc.shouldPass {
					assert.Equal(t, http.StatusOK, w.Code)
					assert.Equal(t, "success", w.Body.String())
				} else {
					assert.Equal(t, http.StatusBadRequest, w.Code)
					var response map[string]interface{}
					err := json.Unmarshal(w.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.Contains(t, response, "error")
				}
			})
		}
	})

	t.Run("Edge cases", func(t *testing.T) {
		testCases := []struct {
			name           string
			serviceID      string
			requiredPrefix string
			shouldPass     bool
		}{
			{"Empty prefix requirement", "svc_00000000000000000000000001", "", false}, // Empty prefix should fail
			{"Prefix with underscores", "demo_svc_00000000000000000000000001", "demo_svc", true},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				svcID := appioid.MustParse(tc.serviceID)
				ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)

				middleware := ValidateServiceIDPrefix(tc.requiredPrefix, logger)
				handler := middleware(mockHandler)

				req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
				w := httptest.NewRecorder()

				handler.ServeHTTP(w, req)

				if tc.shouldPass {
					assert.Equal(t, http.StatusOK, w.Code)
					assert.Equal(t, "success", w.Body.String())
				} else {
					assert.Equal(t, http.StatusBadRequest, w.Code)
					var response map[string]interface{}
					err := json.Unmarshal(w.Body.Bytes(), &response)
					assert.NoError(t, err)
					assert.Contains(t, response, "error")
				}
			})
		}
	})

	t.Run("Context with wrong type", func(t *testing.T) {
		// Put a string instead of *appioid.ID in context
		ctx := context.WithValue(context.Background(), SvcIDKey{}, "not-an-id")

		middleware := ValidateServiceIDPrefix("demo_svc", logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// Should pass through since GetServiceIDFromContext will return false
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Context with nil service ID", func(t *testing.T) {
		// Don't put nil in context, just use empty context
		middleware := ValidateServiceIDPrefix("demo_svc", logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// Should pass through since GetServiceIDFromContext will return false for empty context
		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Multiple middleware chain", func(t *testing.T) {
		svcID := appioid.MustParse("demo_svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)

		// Chain multiple prefix checks
		middleware1 := ValidateServiceIDPrefix("demo_svc", logger)
		middleware2 := ValidateServiceIDPrefix("demo_svc", logger) // Same check again
		handler := middleware1(middleware2(mockHandler))

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Conflicting prefix requirements", func(t *testing.T) {
		svcID := appioid.MustParse("demo_svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), SvcIDKey{}, svcID)

		// Chain conflicting prefix checks
		middleware1 := ValidateServiceIDPrefix("demo_svc", logger) // Should pass
		middleware2 := ValidateServiceIDPrefix("svc", logger)      // Should fail
		handler := middleware1(middleware2(mockHandler))

		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// Should fail at the second middleware
		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})

	t.Run("Real-world demo scenario", func(t *testing.T) {
		// Simulate the actual demo.appio.so route scenario
		demoSvcID := appioid.MustParse("demo_svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), SvcIDKey{}, demoSvcID)

		middleware := ValidateServiceIDPrefix("demo_svc", logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/demo-appio-so/services/demo_svc_00000000000000000000000001", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "success", w.Body.String())
	})

	t.Run("Real-world regular service scenario", func(t *testing.T) {
		// Simulate a regular service trying to access demo route
		regularSvcID := appioid.MustParse("svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), SvcIDKey{}, regularSvcID)

		middleware := ValidateServiceIDPrefix("demo_svc", logger)
		handler := middleware(mockHandler)

		req := httptest.NewRequest("GET", "/demo-appio-so/services/svc_00000000000000000000000001", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		// Should be rejected
		assert.Equal(t, http.StatusBadRequest, w.Code)

		var response map[string]interface{}
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Contains(t, response, "error")
	})
}
