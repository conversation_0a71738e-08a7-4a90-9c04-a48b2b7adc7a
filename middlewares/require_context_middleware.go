package middlewares

import (
	"fmt"
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"go.uber.org/zap"
)

// RequireContext ensures that a context value by the given key is set and not nil.
// If the value is not set or is nil, the middleware returns HTTP 400 Bad Request.
func RequireContext(contextKey any, logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			value := ctx.Value(contextKey)

			if value == nil {
				logger.Error("required context value is missing or nil",
					zap.String("context_key", fmt.Sprintf("%T", contextKey)),
					zap.String("method", r.Method),
					zap.String("path", r.URL.Path))
				helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
				return
			}

			next.Serve<PERSON><PERSON>(w, r)
		})
	}
}
