package middlewares

import (
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/pkg"
	"api.appio.so/services"
	"go.uber.org/zap"
)

// ValidateServiceUser middleware validates that the user has access to the service
// It checks:
// 1. If AuthSvcIDKey is set, that Svc<PERSON><PERSON>ey is equal to it
// 2. If AuthSvcIDKey is not set, it validates that SvcIDKey belongs to UsrIDKey via UserService
func ValidateServiceUser(userService services.UserServiceInterface, logger *zap.Logger) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()

			// Get service ID from context (required)
			svcID, ok := GetServiceIDFromContext(ctx)
			if !ok || svcID == nil {
				logger.Error("service ID not found in context")
				helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
				return
			}

			// Case 1: If AuthSvcIDKey is set, check that <PERSON>vc<PERSON><PERSON>ey equals it
			authSvcID, ok := GetAuthServiceIDFromContext(ctx) // can be null
			if ok && authSvcID != nil {
				if svcID.String() != authSvcID.String() {
					logger.Info("service ID mismatch",
						zap.String("service_id", svcID.String()),
						zap.String("auth_service_id", authSvcID.String()))
					helpers.RenderJSONError(w, r, pkg.ErrForbidden)
					return
				}

				next.ServeHTTP(w, r)
				return
			}

			// Case 2: validate user access to service
			userID, ok := GetUserIDFromContext(ctx)
			if !ok || userID == nil {
				logger.Error("user ID not found in context")
				helpers.RenderJSONError(w, r, pkg.ErrUnauthorized)
				return
			}

			// Validate that the user has access to the service
			err := userService.ValidateUserServiceAccess(ctx, userID, svcID)
			if err != nil {
				helpers.RenderJSONError(w, r, err)
				return
			}

			next.ServeHTTP(w, r)
		})
	}
}
