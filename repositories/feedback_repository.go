package repositories

import (
	"api.appio.so/models"
	"context"
	"fmt"
	"github.com/jackc/pgx/v5"
	"net"

	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5/pgxpool"
)

type FeedbackRepositoryInterface interface {
	Create(ctx context.Context, fdbID *appioid.ID, platform models.Platform, version string, ip net.IP, svcID *appioid.ID, dvcID *appioid.ID, message string) error
}

type FeedbackRepository struct {
	DB *pgxpool.Pool
}

func NewFeedbackRepository(db *pgxpool.Pool) *FeedbackRepository {
	return &FeedbackRepository{
		DB: db,
	}
}

func (r *FeedbackRepository) Create(ctx context.Context, fdbID *appioid.ID, platform models.Platform, version string, ip net.IP, svcID *appioid.ID, dvcID *appioid.ID, message string) error {
	query := `INSERT INTO feedbacks (
					id,
					platform,
					version,
				    ip,
					service_id,
					device_id,
					message
				 ) VALUES (
					@id,
					@platform,
					@version,
				    @ip,
					@service_id,
					@device_id,
					@message
				)`
	args := pgx.NamedArgs{
		"id":         fdbID,
		"platform":   platform,
		"version":    version,
		"ip":         ip.String(),
		"service_id": svcID,
		"device_id":  dvcID,
		"message":    message,
	}

	_, err := r.DB.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("creating feedback: %w", err)
	}
	return nil
}
