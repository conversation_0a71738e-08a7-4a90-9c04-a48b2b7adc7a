package repositories

import (
	"api.appio.so/models"
	"context"
	"fmt"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type DeviceServiceRepositoryInterface interface {
	Create(ctx context.Context, dvcsvcID, dvcID, svcID *appioid.ID, customerUserID string) error
	CreateTx(tx pgx.Tx, ctx context.Context, dvcsvcID, dvcID, svcID *appioid.ID, customerUserID string) error
	Deactivate(ctx context.Context, svcID, dvcID *appioid.ID) (bool, error)
	DeactivateByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]appioid.ID, error)
	UpdateTx(tx pgx.Tx, ctx context.Context, svcID, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error)
}

type DeviceServiceRepository struct {
	DB *pgxpool.Pool
}

func NewDeviceServiceRepository(db *pgxpool.Pool) *DeviceServiceRepository {
	return &DeviceServiceRepository{
		DB: db,
	}
}

func (r *DeviceServiceRepository) Create(ctx context.Context, dvcsvcID, dvcID, svcID *appioid.ID, customerUserID string) error {
	return r.createWithExecutor(r.DB, ctx, dvcsvcID, dvcID, svcID, customerUserID)
}

func (r *DeviceServiceRepository) CreateTx(tx pgx.Tx, ctx context.Context, dvcsvcID, dvcID, svcID *appioid.ID, customerUserID string) error {
	return r.createWithExecutor(tx, ctx, dvcsvcID, dvcID, svcID, customerUserID)
}

// Deactivate only active devices
func (r *DeviceServiceRepository) Deactivate(ctx context.Context, svcID, dvcID *appioid.ID) (bool, error) {
	query := `UPDATE dvc_svc SET deactivated_at=NOW() WHERE service_id=@service_id AND device_id=@device_id AND deactivated_at IS NULL`
	args := pgx.NamedArgs{
		"service_id": svcID,
		"device_id":  dvcID,
	}

	cmdTag, err := r.DB.Exec(ctx, query, args)
	if err != nil {
		return false, fmt.Errorf("deactivating device-service: %w", err)
	}
	return cmdTag.RowsAffected() > 0, nil
}

func (r *DeviceServiceRepository) DeactivateByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]appioid.ID, error) {
	query := `UPDATE dvc_svc
		SET deactivated_at=NOW()
		
		WHERE service_id=@service_id
		  AND customer_user_id=@customer_user_id
		  AND deactivated_at IS NULL
		  
	  	RETURNING id`
	args := pgx.NamedArgs{
		"service_id":       svcID,
		"customer_user_id": customerUserID,
	}

	rows, err := r.DB.Query(ctx, query, args)
	if err != nil {
		return nil, fmt.Errorf("deactivating device-service by customerUserID: %w", err)
	}
	defer rows.Close()

	var ids []appioid.ID
	for rows.Next() {
		var id string
		if err := rows.Scan(&id); err != nil {
			return nil, fmt.Errorf("scanning row: %w", err)
		}
		appioID := appioid.MustParse(id)
		ids = append(ids, *appioID)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("iterating rows: %w", err)
	}

	return ids, nil

}

func (r *DeviceServiceRepository) UpdateTx(tx pgx.Tx, ctx context.Context, svcID, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error) {
	return r.updateWithExecutor(tx, ctx, svcID, dvcID, dvcUpdateReq)
}

// --------------------------------------------------------------------------------------------------------------------

// (device_id, service_id) duplicates for active record are prevented via UNIQUE constraint
func (r *DeviceServiceRepository) createWithExecutor(executor SQLExecutor, ctx context.Context, dvcsvcID, dvcID, svcID *appioid.ID, customerUserID string) error {
	query := `INSERT INTO dvc_svc (
					id,
					device_id,
					service_id,
					customer_user_id,
					notifications_enabled
				 ) VALUES (
					@id,
					@device_id,
					@service_id,
					@customer_user_id,
				    @notifications_enabled
				)
				ON CONFLICT DO NOTHING
				`
	args := pgx.NamedArgs{
		"id":                    dvcsvcID,
		"device_id":             dvcID,
		"service_id":            svcID,
		"service_id_2":          svcID,
		"customer_user_id":      customerUserID,
		"notifications_enabled": true, // every new connection starts with active notifications
	}

	_, err := executor.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("creating device-service: %w", err)
	}
	return nil
}

func (r *DeviceServiceRepository) updateWithExecutor(executor SQLExecutor, ctx context.Context, svcID, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error) {
	query := "UPDATE dvc_svc SET notifications_enabled=@notifications_enabled WHERE service_id=@service_id AND device_id=@device_id AND deactivated_at IS NULL"
	args := pgx.NamedArgs{
		"service_id":            svcID,
		"device_id":             dvcID,
		"notifications_enabled": true, // TODO: notifications per service. for now always true. This is different vs Device.NotificationsEnabled dvcUpdateReq.NotificationsEnabled,
	}

	cmdTag, err := executor.Exec(ctx, query, args)
	if err != nil {
		return false, fmt.Errorf("updating device-service: %w", err)
	}
	return cmdTag.RowsAffected() > 0, nil
}
