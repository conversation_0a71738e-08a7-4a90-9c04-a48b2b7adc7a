package repositories

import (
	"api.appio.so/models"
	"api.appio.so/models/notification_status"
	"api.appio.so/models/notification_type"
	"context"
	"fmt"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type NotificationDeliveryRepositoryInterface interface {
	ListByDevice(ctx context.Context, svcID, dvcID *appioid.ID, statuses []notification_status.Status) ([]models.NotificationResponse, error)
	ListByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string, statuses []notification_status.Status) ([]models.NotificationResponse, error)
	Create(ctx context.Context, ntfdlvID, notificationID, dvcID *appioid.ID, status notification_status.Status) error
	CreateTx(tx pgx.Tx, ctx context.Context, ntfdlvID, notificationID, dvcID *appioid.ID, status notification_status.Status) error
	Update(ctx context.Context, svcID, ntfID, dvcID *appioid.ID, status notification_status.Status) (bool, error)
}

type NotificationDeliveryRepository struct {
	DB *pgxpool.Pool
}

func NewNotificationDeliveryRepository(db *pgxpool.Pool) *NotificationDeliveryRepository {
	return &NotificationDeliveryRepository{
		DB: db,
	}
}

func (r *NotificationDeliveryRepository) ListByDevice(ctx context.Context, svcID, dvcID *appioid.ID, statuses []notification_status.Status) ([]models.NotificationResponse, error) {
	query := `SELECT 
			n.id, 
			n.service_id, 
			n.payload,
			n.scheduled_at,
			nd.status
		FROM notification_deliveries nd
		JOIN notifications n on n.id=nd.notification_id
		JOIN dvc_svc ds ON ds.service_id=n.service_id AND ds.device_id=nd.device_id
		JOIN services s ON s.id=n.service_id
		
		WHERE n.status = ANY(@statuses::queue_status[])
		  AND n.type=@type
		  AND n.service_id=@service_id
		  AND nd.device_id=@device_id
		    
		GROUP BY n.id, n.service_id, n.payload, n.scheduled_at, nd.status, nd.updated_at, nd.device_id, ds.customer_user_id
		ORDER BY nd.updated_at DESC`
	args := pgx.NamedArgs{
		"statuses":   statusToStringSlice(statuses),
		"type":       notification_type.Foreground,
		"service_id": svcID,
		"device_id":  dvcID,
	}
	return QueryList[models.NotificationResponse](ctx, r.DB, query, args)
}

func (r *NotificationDeliveryRepository) ListByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string, statuses []notification_status.Status) ([]models.NotificationResponse, error) {
	query := `SELECT 
			n.id, 
			n.service_id, 
			n.payload,
			n.scheduled_at,
			nd.status
		FROM notification_deliveries nd
		JOIN notifications n on n.id=nd.notification_id
		JOIN dvc_svc ds ON ds.service_id=n.service_id AND ds.device_id=nd.device_id
		JOIN services s ON s.id=n.service_id
		
		WHERE n.status = ANY(@statuses::queue_status[])
		  AND n.type=@type
		  AND n.service_id=@service_id
		  AND ds.customer_user_id=@customer_user_id
		    
		GROUP BY n.id, n.service_id, n.payload, n.scheduled_at, nd.status, nd.updated_at, nd.device_id, ds.customer_user_id
		ORDER BY nd.updated_at DESC`
	args := pgx.NamedArgs{
		"statuses":         statusToStringSlice(statuses),
		"type":             notification_type.Foreground,
		"service_id":       svcID,
		"customer_user_id": customerUserID,
	}
	return QueryList[models.NotificationResponse](ctx, r.DB, query, args)
}

func (r *NotificationDeliveryRepository) Create(ctx context.Context, ntfdlvID, notificationID, dvcID *appioid.ID, status notification_status.Status) error {
	return r.createWithExecutor(r.DB, ctx, ntfdlvID, notificationID, dvcID, status)
}

func (r *NotificationDeliveryRepository) CreateTx(tx pgx.Tx, ctx context.Context, ntfdlvID, notificationID, dvcID *appioid.ID, status notification_status.Status) error {
	return r.createWithExecutor(tx, ctx, ntfdlvID, notificationID, dvcID, status)
}

// Overwrite status regardless of what the original value is
func (r *NotificationDeliveryRepository) Update(ctx context.Context, svcID, ntfID, dvcID *appioid.ID, status notification_status.Status) (bool, error) {
	query := `UPDATE notification_deliveries nd SET
					status=@status,
					updated_at=NOW()
			  FROM notifications n
			  WHERE
			      	n.id=nd.notification_id AND
			      	n.service_id=@service_id AND
			      	nd.notification_id=@notification_id AND
			      	nd.device_id=@device_id`
	args := pgx.NamedArgs{
		"service_id":      svcID,
		"notification_id": ntfID,
		"device_id":       dvcID,
		"status":          status,
	}

	cmdTag, err := r.DB.Exec(ctx, query, args)
	if err != nil {
		return false, fmt.Errorf("updating notification delivery: %w", err)
	}
	return cmdTag.RowsAffected() > 0, nil
}

// --------------------------------------------------------------------------------------------------------------------

func (r *NotificationDeliveryRepository) createWithExecutor(executor SQLExecutor, ctx context.Context, ntfdlvID, notificationID, dvcID *appioid.ID, status notification_status.Status) error {
	query := `INSERT INTO notification_deliveries (
					id,
					notification_id,
					device_id,
					status,
					updated_at
				 ) VALUES (
					@id,
					@notification_id,
					@device_id,
					@status,
				    CURRENT_TIMESTAMP
				)`
	args := pgx.NamedArgs{
		"id":              ntfdlvID,
		"notification_id": notificationID,
		"device_id":       dvcID,
		"status":          status,
	}

	_, err := executor.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("creating notification delivery: %w", err)
	}
	return nil
}

func statusToStringSlice(statuses []notification_status.Status) []string {
	out := make([]string, len(statuses))
	for i, s := range statuses {
		out[i] = s.String()
	}
	return out
}
