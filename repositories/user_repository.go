package repositories

import (
	"context"

	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

const (
	validateUserServiceAccessQuery = `
		SELECT EXISTS (
			SELECT 1
			FROM users u
			JOIN organizations o ON u.organization_id = o.id
			JOIN services s ON s.organization_id = o.id
			WHERE u.id = @user_id
			  AND s.id = @service_id
			  AND u.deactivated_at IS NULL
			  AND o.deactivated_at IS NULL
			  AND s.deactivated_at IS NULL
		)
	`
)

type UserRepositoryInterface interface {
	ValidateUserServiceAccess(ctx context.Context, userID, serviceID *appioid.ID) bool
}

type UserRepository struct {
	DB *pgxpool.Pool
}

func NewUserRepository(db *pgxpool.Pool) *UserRepository {
	return &UserRepository{
		DB: db,
	}
}

// ValidateUserServiceAccess checks that the given user_id belongs to an active organization
// and that the given service_id is active and belongs to the same organization
func (r *UserRepository) ValidateUserServiceAccess(ctx context.Context, userID, serviceID *appioid.ID) bool {
	args := pgx.NamedArgs{
		"user_id":    userID,
		"service_id": serviceID,
	}
	var exists bool
	if err := r.DB.QueryRow(ctx, validateUserServiceAccessQuery, args).Scan(&exists); err != nil {
		return false
	}
	return exists
}
