package repositories

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewAPIKeyRepository(t *testing.T) {
	t.Run("Creates repository correctly", func(t *testing.T) {
		repo := NewAPIKeyRepository(nil)

		assert.NotNil(t, repo)
		assert.Nil(t, repo.DB) // DB is nil in this test
	})

	t.Run("Creates repository with correct type", func(t *testing.T) {
		repo := NewAPIKeyRepository(nil)

		assert.NotNil(t, repo)
		assert.IsType(t, &APIKeyRepository{}, repo)
	})
}

func TestAPIKeyRepository_InterfaceCompliance(t *testing.T) {
	t.Run("Repository implements interface", func(t *testing.T) {
		repo := NewAPIKeyRepository(nil)

		// Verify that the repository implements the interface
		var _ APIKeyRepositoryInterface = repo

		assert.NotNil(t, repo)
	})

	t.Run("Repository structure validation", func(t *testing.T) {
		repo := NewAPIKeyRepository(nil)

		// Verify the repository has the expected structure
		assert.NotNil(t, repo)
		assert.Nil(t, repo.DB) // DB is nil in this test
		assert.IsType(t, &APIKeyRepository{}, repo)
	})
}

// Note: The IsActiveAPIKey method requires a database connection to test properly.
// Integration tests would be needed to test the actual database operations.
// For now, we're testing the constructor and ensuring the repository structure is correct.

func TestAPIKeyRepository_IsActiveAPIKey_Structure(t *testing.T) {
	t.Run("Repository has correct interface", func(t *testing.T) {
		repo := NewAPIKeyRepository(nil)

		// Verify that the repository implements the interface
		var _ APIKeyRepositoryInterface = repo

		assert.NotNil(t, repo)
	})

	t.Run("Interface compliance", func(t *testing.T) {
		repo := NewAPIKeyRepository(nil)

		// Verify interface compliance by checking method signature exists
		// This ensures the method signature matches the interface
		var _ APIKeyRepositoryInterface = repo

		assert.NotNil(t, repo)
		assert.IsType(t, &APIKeyRepository{}, repo)
	})
}
