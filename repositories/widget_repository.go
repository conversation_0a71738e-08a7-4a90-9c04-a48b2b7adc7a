package repositories

import (
	"context"
	"fmt"

	"api.appio.so/models"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type WidgetRepositoryInterface interface {
	FindByID(ctx context.Context, svcID, wgtID *appioid.ID) (*models.Widget, error)
	List(ctx context.Context, svcID *appioid.ID) ([]models.Widget, error)
	Create(ctx context.Context, svcID, wgtID *appioid.ID, wgtReq models.WidgetRequest) error
	Update(ctx context.Context, svcID, wgtID *appioid.ID, wgtReq models.WidgetRequest) (bool, error)
	Delete(ctx context.Context, svcID, wgtID *appioid.ID) (bool, error)
}

type WidgetRepository struct {
	DB *pgxpool.Pool
}

func NewWidgetRepository(db *pgxpool.Pool) *WidgetRepository {
	return &WidgetRepository{
		DB: db,
	}
}

// FindByID Only not deleted
func (r *WidgetRepository) FindByID(ctx context.Context, svcID, wgtID *appioid.ID) (*models.Widget, error) {
	var wgt models.Widget
	query := `SELECT w.id, w.service_id, w.template, w.source, w.created_at, w.deleted_at 
		FROM widgets w
		JOIN services s ON s.id=w.service_id
		WHERE w.id=@id AND w.service_id=@service_id AND w.deleted_at IS NULL`
	args := pgx.NamedArgs{
		"id":         wgtID,
		"service_id": svcID,
	}
	err := r.DB.QueryRow(ctx, query, args).Scan(
		&wgt.ID,
		&wgt.ServiceID,
		&wgt.Template,
		&wgt.Source,
		&wgt.CreatedAt,
		&wgt.DeletedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("finding widget: %w", err)
	}
	return &wgt, nil
}

func (r *WidgetRepository) List(ctx context.Context, svcID *appioid.ID) ([]models.Widget, error) {
	query := `
        SELECT w.id, w.service_id, w.template, w.source, w.created_at, w.deleted_at 
        FROM widgets w
        JOIN services s ON s.id=w.service_id
        WHERE w.service_id=@service_id AND w.deleted_at IS NULL
		ORDER BY created_at DESC`
	args := pgx.NamedArgs{
		"service_id": svcID,
	}
	return QueryList[models.Widget](ctx, r.DB, query, args)
}

func (r *WidgetRepository) Create(ctx context.Context, svcID, wgtID *appioid.ID, wgtReq models.WidgetRequest) error {
	query := `INSERT INTO widgets (
					id,
					service_id,
					template,
					source
				 ) VALUES (
					@id,
					@service_id,
					@template,
				    @source
				)`
	args := pgx.NamedArgs{
		"id":         wgtID,
		"service_id": svcID,
		"template":   wgtReq.Template,
		"source":     wgtReq.Source,
	}

	_, err := r.DB.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("creating widget: %w", err)
	}
	return nil
}

func (r *WidgetRepository) Update(ctx context.Context, svcID, wgtID *appioid.ID, wgtReq models.WidgetRequest) (bool, error) {
	query := `UPDATE widgets SET template=@template, source=@source WHERE id=@id AND service_id=@service_id AND deleted_at IS NULL`
	args := pgx.NamedArgs{
		"template":   wgtReq.Template,
		"source":     wgtReq.Source,
		"id":         wgtID,
		"service_id": svcID,
	}

	cmdTag, err := r.DB.Exec(ctx, query, args)
	if err != nil {
		return false, fmt.Errorf("updating widget: %w", err)
	}
	return cmdTag.RowsAffected() > 0, nil
}

func (r *WidgetRepository) Delete(ctx context.Context, svcID, wgtID *appioid.ID) (bool, error) {
	query := `UPDATE widgets SET deleted_at=NOW() WHERE id=@id AND service_id=@service_id AND deleted_at IS NULL`
	args := pgx.NamedArgs{
		"id":         wgtID,
		"service_id": svcID,
	}

	cmdTag, err := r.DB.Exec(ctx, query, args)
	if err != nil {
		return false, fmt.Errorf("deleting widget: %w", err)
	}
	return cmdTag.RowsAffected() > 0, nil
}
