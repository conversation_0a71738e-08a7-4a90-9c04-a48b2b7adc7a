package repositories

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewDeviceRepository(t *testing.T) {
	t.Run("Creates repository correctly", func(t *testing.T) {
		repo := NewDeviceRepository(nil)

		assert.NotNil(t, repo)
		assert.Nil(t, repo.DB) // DB is nil in this test
	})

	t.Run("Creates repository with correct type", func(t *testing.T) {
		repo := NewDeviceRepository(nil)

		assert.NotNil(t, repo)
		assert.IsType(t, &DeviceRepository{}, repo)
	})
}

func TestDeviceRepository_InterfaceCompliance(t *testing.T) {
	t.Run("Repository implements interface", func(t *testing.T) {
		repo := NewDeviceRepository(nil)

		// Verify that the repository implements the interface
		var _ DeviceRepositoryInterface = repo

		assert.NotNil(t, repo)
	})

	t.Run("Repository structure validation", func(t *testing.T) {
		repo := NewDeviceRepository(nil)

		// Verify the repository has the expected structure
		assert.NotNil(t, repo)
		assert.Nil(t, repo.DB) // DB is nil in this test
		assert.IsType(t, &DeviceRepository{}, repo)
	})
}

// Note: The UpdateLastSeenAt method requires a database connection to test properly.
// Integration tests would be needed to test the actual database operations.
// For now, we're testing the constructor and ensuring the repository structure is correct.

func TestDeviceRepository_UpdateLastSeenAt_Structure(t *testing.T) {
	t.Run("UpdateLastSeenAt method exists", func(t *testing.T) {
		repo := NewDeviceRepository(nil)

		// This ensures the UpdateLastSeenAt method exists with the correct signature
		// We use interface assignment to verify method existence
		var iface DeviceRepositoryInterface = repo
		assert.NotNil(t, iface)

		// Verify the repository type
		assert.IsType(t, (*DeviceRepository)(nil), iface)
	})
}
