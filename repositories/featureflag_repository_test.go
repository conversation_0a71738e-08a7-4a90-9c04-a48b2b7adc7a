package repositories

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewFeatureFlagRepository(t *testing.T) {
	t.Run("Creates repository correctly", func(t *testing.T) {
		repo := NewFeatureFlagRepository(nil)
		assert.NotNil(t, repo)
		assert.Nil(t, repo.DB)
	})

	t.Run("Creates repository with correct type", func(t *testing.T) {
		repo := NewFeatureFlagRepository(nil)
		assert.NotNil(t, repo)
		assert.IsType(t, &FeatureFlagRepository{}, repo)
	})
}
