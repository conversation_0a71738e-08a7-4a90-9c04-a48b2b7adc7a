package repositories

import (
	"context"
	"fmt"
	"time"

	"api.appio.so/models"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type FingerprintRepositoryInterface interface {
	ListBy(ctx context.Context, freq models.FingerprintMatchRequest) ([]models.Fingerprint, error)
	Create(ctx context.Context, fingID *appioid.ID, freq models.FingerprintCreateRequest) error
}

type FingerprintRepository struct {
	DB         *pgxpool.Pool
	timeWindow time.Duration
}

func NewFingerprintRepository(db *pgxpool.Pool, timeWindow time.Duration) *FingerprintRepository {
	return &FingerprintRepository{
		DB:         db,
		timeWindow: timeWindow,
	}
}

func (r *FingerprintRepository) ListBy(ctx context.Context, freq models.FingerprintMatchRequest) ([]models.Fingerprint, error) {
	query := `SELECT DISTINCT ON (service_id, customer_user_id) 
				id, service_id, created_at, customer_user_id, ip, user_agent, screen_resolution, language, time_offset, data
			FROM fingerprints 
			WHERE
				ip=@ip AND
				user_agent=@user_agent AND
				screen_resolution=@screen_resolution AND
				language=@language AND 
				time_offset=@time_offset AND
				created_at >= NOW() AT TIME ZONE 'UTC' - INTERVAL '1 second' * @time_window
			ORDER BY service_id, customer_user_id, created_at DESC
			LIMIT 2` // selecting 2 is enough because if more than 1 row exist, it is not a match
	args := pgx.NamedArgs{
		"ip":                freq.IP.String(), // when using just freq.IP it doesn't work
		"user_agent":        freq.UserAgent,
		"screen_resolution": freq.ScreenResolution,
		"language":          freq.Language,
		"time_offset":       freq.TimeOffset,
		"time_window":       int(r.timeWindow.Seconds()),
	}
	return QueryList[models.Fingerprint](ctx, r.DB, query, args)
}

func (r *FingerprintRepository) Create(ctx context.Context, fingID *appioid.ID, freq models.FingerprintCreateRequest) error {
	query := `INSERT INTO fingerprints (
                          id,
						  service_id,
                          customer_user_id,
                          ip,
                          user_agent,
                          screen_resolution,
                          language,
                          time_offset,
                          data
				) VALUES (
				          @id,
						  @service_id,
				          @customer_user_id,
				          @ip,
				          @user_agent,
				          @screen_resolution,
				          @language,
				          @time_offset,
				          @data
			)`
	args := pgx.NamedArgs{
		"id":                fingID,
		"service_id":        freq.ServiceID,
		"customer_user_id":  freq.CustomerUserID,
		"ip":                freq.IP.String(),
		"user_agent":        freq.Data.UserAgent,
		"screen_resolution": freq.Data.ScreenResolution,
		"language":          freq.Data.Language,
		"time_offset":       freq.Data.TimeOffset,
		"data":              freq.Data,
	}

	_, err := r.DB.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("creating fingerprint: %w", err)
	}
	return nil
}
