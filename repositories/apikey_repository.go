package repositories

import (
	"context"
	"fmt"

	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type APIKeyRepositoryInterface interface {
	IsActiveAPIKey(ctx context.Context, apiKey string) bool
	FindBy(ctx context.Context, apiKey string) (usrID *appioid.ID, svcID *appioid.ID, err error)
}

type APIKeyRepository struct {
	DB *pgxpool.Pool
}

func NewAPIKeyRepository(db *pgxpool.Pool) *APIKeyRepository {
	return &APIKeyRepository{
		DB: db,
	}
}

func (r *APIKeyRepository) IsActiveAPIKey(ctx context.Context, apiKey string) bool {
	query := `
		SELECT EXISTS (
			SELECT 1 FROM api_keys
			WHERE api_key=@api_key AND deactivated_at IS NULL
		)`
	args := pgx.NamedArgs{"api_key": apiKey}
	var active bool
	if err := r.DB.QueryRow(ctx, query, args).Scan(&active); err != nil {
		return false
	}
	return active
}

func (r *APIKeyRepository) FindBy(ctx context.Context, apiKey string) (usrID *appioid.ID, svcID *appioid.ID, err error) {
	query := `
		SELECT user_id, service_id FROM api_keys
		WHERE api_key=@api_key AND deactivated_at IS NULL
		`
	args := pgx.NamedArgs{"api_key": apiKey}

	err = r.DB.QueryRow(ctx, query, args).Scan(&usrID, &svcID)
	if err != nil {
		return nil, nil, fmt.Errorf("getting data by API key: %w", err)
	}
	return usrID, svcID, nil
}
