package repositories

import (
	"api.appio.so/models/notification_status"
	"api.appio.so/models/notification_type"
	"context"
	"fmt"

	"api.appio.so/models"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

type NotificationRepositoryInterface interface {
	FindByID(ctx context.Context, svcID, ntfID *appioid.ID) (*models.NotificationResponseWithStats, error)
	List(ctx context.Context, svcID *appioid.ID, statuses []notification_status.Status) ([]models.NotificationResponse, error)
	ListWithStats(ctx context.Context, svcID *appioid.ID, statuses []notification_status.Status) ([]models.NotificationResponseWithStats, error)
	Create(ctx context.Context, t notification_type.Type, ntfID, svcID *appioid.ID, ntfReq models.NotificationRequest, status notification_status.Status) error
	CreateTx(tx pgx.Tx, ctx context.Context, t notification_type.Type, ntfID, svcID *appioid.ID, ntfReq models.NotificationRequest, status notification_status.Status) error
}

type NotificationRepository struct {
	DB *pgxpool.Pool
}

func NewNotificationRepository(db *pgxpool.Pool) *NotificationRepository {
	return &NotificationRepository{
		DB: db,
	}
}

func (r *NotificationRepository) FindByID(ctx context.Context, svcID, ntfID *appioid.ID) (*models.NotificationResponseWithStats, error) {
	var ntfWithStats models.NotificationResponseWithStats
	query := `
		SELECT 
			n.id, 
			n.service_id,  
			n.scheduled_at,
			n.payload, 
			n.status,
			COUNT(nd.id)                                          AS total,
			COUNT(*) FILTER (WHERE nd.status = @status_created)   AS created,
			COUNT(*) FILTER (WHERE nd.status = @status_queued)    AS queued,
			COUNT(*) FILTER (WHERE nd.status = @status_completed) AS completed,
			COUNT(*) FILTER (WHERE nd.status = @status_failed)    AS failed
		
		FROM notifications n
		LEFT JOIN notification_deliveries nd ON nd.notification_id=n.id
		
		WHERE n.id=@id AND n.type=@type AND n.service_id=@service_id
		
		GROUP BY n.id, n.service_id, n.created_at, n.payload, n.status`
	args := pgx.NamedArgs{
		"id":               ntfID,
		"type":             notification_type.Foreground,
		"service_id":       svcID,
		"status_created":   notification_status.Created,
		"status_queued":    notification_status.Queued,
		"status_completed": notification_status.Completed,
		"status_failed":    notification_status.Failed,
	}
	err := r.DB.QueryRow(ctx, query, args).Scan(
		&ntfWithStats.ID,
		&ntfWithStats.ServiceID,
		&ntfWithStats.ScheduledAt,
		&ntfWithStats.Payload,
		&ntfWithStats.Status,
		&ntfWithStats.DeliveryStats.Total,
		&ntfWithStats.DeliveryStats.Created,
		&ntfWithStats.DeliveryStats.Queued,
		&ntfWithStats.DeliveryStats.Completed,
		&ntfWithStats.DeliveryStats.Failed,
	)

	if err != nil {
		return nil, fmt.Errorf("finding notification with stats: %w", err)
	}

	return &ntfWithStats, nil
}

func (r *NotificationRepository) List(ctx context.Context, svcID *appioid.ID, statuses []notification_status.Status) ([]models.NotificationResponse, error) {
	query := `SELECT 
			n.id, 
			n.service_id,  
			n.scheduled_at,
			n.payload, 
			n.status
		FROM notifications n
		JOIN services s ON s.id=n.service_id
		
		WHERE n.status = ANY(@statuses::queue_status[])
		  AND n.type=@type
		  AND n.service_id=@service_id
		
		GROUP BY n.id, n.service_id, n.created_at, n.payload, n.status
		ORDER BY n.created_at DESC`
	args := pgx.NamedArgs{
		"statuses":   statusToStringSlice(statuses),
		"type":       notification_type.Foreground,
		"service_id": svcID,
	}
	return QueryList[models.NotificationResponse](ctx, r.DB, query, args)
}

func (r *NotificationRepository) ListWithStats(ctx context.Context, svcID *appioid.ID, statuses []notification_status.Status) ([]models.NotificationResponseWithStats, error) {
	query := `SELECT 
			n.id, 
			n.service_id,
			n.scheduled_at,
			n.payload, 
			n.status,
			COUNT(nd.id)                                          AS total,
			COUNT(*) FILTER (WHERE nd.status = @status_created)   AS created,
			COUNT(*) FILTER (WHERE nd.status = @status_queued)    AS queued,
			COUNT(*) FILTER (WHERE nd.status = @status_completed) AS completed,
			COUNT(*) FILTER (WHERE nd.status = @status_failed)    AS failed
		FROM notifications n
		LEFT JOIN notification_deliveries nd ON nd.notification_id=n.id
		
		WHERE n.status = ANY(@statuses::queue_status[]) 
		  AND n.type=@type
		  AND n.service_id=@service_id
		
		GROUP BY n.id, n.service_id, n.created_at, n.payload, n.status
		ORDER BY n.created_at DESC`
	args := pgx.NamedArgs{
		"statuses":         statusToStringSlice(statuses),
		"type":             notification_type.Foreground,
		"service_id":       svcID,
		"status_created":   notification_status.Created,
		"status_queued":    notification_status.Queued,
		"status_completed": notification_status.Completed,
		"status_failed":    notification_status.Failed,
	}
	rows, err := r.DB.Query(ctx, query, args)
	if err != nil {
		return nil, fmt.Errorf("selecting notifications with stats: %w", err)
	}
	defer rows.Close()

	var listWithStats []models.NotificationResponseWithStats
	for rows.Next() {
		var n models.NotificationResponseWithStats
		err := rows.Scan(
			&n.ID,
			&n.ServiceID,
			&n.ScheduledAt,
			&n.Payload,
			&n.Status,
			&n.DeliveryStats.Total,
			&n.DeliveryStats.Created,
			&n.DeliveryStats.Queued,
			&n.DeliveryStats.Completed,
			&n.DeliveryStats.Failed,
		)
		if err != nil {
			return nil, fmt.Errorf("scanning row: %w", err)
		}
		listWithStats = append(listWithStats, n)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("iterating rows: %w", err)
	}

	return listWithStats, nil
}

func (r *NotificationRepository) Create(ctx context.Context, t notification_type.Type, ntfID, svcID *appioid.ID, ntfReq models.NotificationRequest, status notification_status.Status) error {
	return r.createWithExecutor(r.DB, ctx, t, ntfID, svcID, ntfReq, status)
}

func (r *NotificationRepository) CreateTx(tx pgx.Tx, ctx context.Context, t notification_type.Type, ntfID, svcID *appioid.ID, ntfReq models.NotificationRequest, status notification_status.Status) error {
	return r.createWithExecutor(tx, ctx, t, ntfID, svcID, ntfReq, status)
}

// -------------------------------------------------------------------------------------------------------------------

func (r *NotificationRepository) createWithExecutor(executor SQLExecutor, ctx context.Context, t notification_type.Type, ntfID, svcID *appioid.ID, ntfReq models.NotificationRequest, status notification_status.Status) error {
	var query string
	var args pgx.NamedArgs

	if ntfReq.ScheduledAt != nil {
		query = `INSERT INTO notifications (
                           id,
                           service_id,
                           scheduled_at,
                           type,
                           payload,
                           status
			     ) VALUES (
			               @id,
			               @service_id,
			               @scheduled_at,
					       @type,
			               @payload,
			               @status
			     )`
		args = pgx.NamedArgs{
			"type":         t,
			"id":           ntfID,
			"service_id":   svcID,
			"scheduled_at": ntfReq.ScheduledAt,
			"payload":      ntfReq.Payload,
			"status":       status,
		}
	} else {
		query = `INSERT INTO notifications (
                           id,
                           service_id,
                           type,
                           payload,
                           status
			   	 ) VALUES (
			   	           @id,
			   	           @service_id,
			   	           @type,
			   	           @payload,
			   	           @status
			     )`
		args = pgx.NamedArgs{
			"type":       t,
			"id":         ntfID,
			"service_id": svcID,
			"payload":    ntfReq.Payload,
			"status":     status,
		}
	}

	_, err := executor.Exec(ctx, query, args)
	if err != nil {
		return fmt.Errorf("creating notifiation: %w", err)
	}
	return nil
}
