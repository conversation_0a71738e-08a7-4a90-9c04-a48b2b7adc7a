package repositories

import (
	"api.appio.so/models"
	"context"
	"fmt"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
)

const (
	findMarketingNameQuery = `SELECT name FROM device_names WHERE brand = @brand AND model = @model LIMIT 1`
	importDeviceNameQuery  = `
		INSERT INTO device_names (brand, model, name)
		VALUES (@brand, @model, @name)
		ON CONFLICT (brand, model) DO UPDATE SET name = EXCLUDED.name
	`
)

type DeviceNameRepositoryInterface interface {
	FindMarketingName(ctx context.Context, brand, model string) (string, error)
	Import(ctx context.Context, deviceNames []models.DeviceName) error
}

type DeviceNameRepository struct {
	DB *pgxpool.Pool
}

func NewDeviceNameRepository(db *pgxpool.Pool) *DeviceNameRepository {
	return &DeviceNameRepository{
		DB: db,
	}
}

func (r *DeviceNameRepository) FindMarketingName(ctx context.Context, brand, model string) (string, error) {
	var marketingName string
	args := pgx.NamedArgs{
		"brand": strings.ToLower(brand),
		"model": strings.ToLower(model),
	}

	err := r.DB.QueryRow(ctx, findMarketingNameQuery, args).Scan(&marketingName)
	if err != nil {
		return "", fmt.Errorf("finding marketing name: %w", err)
	}
	return marketingName, err
}

func (r *DeviceNameRepository) Import(ctx context.Context, deviceNames []models.DeviceName) error {
	if len(deviceNames) == 0 {
		return nil
	}

	tx, err := r.DB.Begin(ctx)
	if err != nil {
		return fmt.Errorf("begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Insert/update all entries
	for _, deviceName := range deviceNames {
		args := pgx.NamedArgs{
			"brand": strings.ToLower(deviceName.Brand),
			"model": strings.ToLower(deviceName.Model),
			"name":  deviceName.Name,
		}
		_, err := tx.Exec(ctx, importDeviceNameQuery, args)
		if err != nil {
			return fmt.Errorf("importing device name %s %s: %w", deviceName.Brand, deviceName.Model, err)
		}
	}

	if err := tx.Commit(ctx); err != nil {
		return fmt.Errorf("commit transaction: %w", err)
	}

	return nil
}
