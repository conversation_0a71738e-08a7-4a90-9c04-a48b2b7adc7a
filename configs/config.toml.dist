[server]
    name = "api.appio.so"
    env = "prod"
    port = 8082
    log_level = "error"
    timeout = "30s"

[auth]
    app = "prod_zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz"
    demo = "prod_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    ios = "prod_yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy"
    android = "prod_wwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwwww"

[db]
    source = "postgres://appio_api:password@localhost:5432/appio?sslmode=disable"
    source_fing = "postgres://appio_fingerprint:password@localhost:5432/fingerprints?sslmode=disable"
    log_queries = false

[fingerprint]
    log = false

[sentry]
    dsn = ""
    send_default_pii = true
    trace_sample_rate = 1.0
