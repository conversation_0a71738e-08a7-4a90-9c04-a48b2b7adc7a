[server]
    name = "cron.api.appio.so"
    env = "prod"
    log_level = "error"

[db]
    source = "postgres://appio_api:password@localhost:5432/appio?sslmode=disable"
    log_queries = false

[ios]
    key_file = ".keys/AuthKey_Production_KZK74JKWR5.p8"
    key_id = "KZK74JKWR5"
    team_id = "V9P4DCJF8Z"
    topic = "so.appio.app"
    server_name = "api.push.apple.com"
    default_category = "APPIO_DEFAULT"
    device_names_url = "https://api.appledb.dev/device/main.json"

[android]
    key_file = ".keys/firebase-prod.json"
    base_url = "https://fcm.googleapis.com/v1/projects/appio-production"
    device_names_url = "https://storage.googleapis.com/play_public/supported_devices.csv"

[sentry]
    dsn = ""
    send_default_pii = true
    trace_sample_rate = 1.0