api.appio.so {
        root * /var/www/api.appio.so

        # TODO: delete this once ios v1.4 is released
        @ios path_regexp ios ^/ios/(.*)$
        handle @ios {
            rewrite * /mobile/{re.ios.1}?{query}
            reverse_proxy localhost:8082
        }

        # Port configured in configs/config.toml
        # Also handling error pages
        handle {
            reverse_proxy localhost:8082
        }

        encode zstd gzip
        header -Server # Remove the Server header

        # SSL enabled in :443 {}}
        # tls /etc/caddy/certs/cert.pem /etc/caddy/certs/key.pem
        log {
                output file /var/log/caddy/api.appio.so-access.log
        }
        log {
                level ERROR
                output file /var/log/caddy/api.appio.so-error.log
        }
}