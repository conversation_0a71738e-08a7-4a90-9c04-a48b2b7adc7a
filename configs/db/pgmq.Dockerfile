# Once I ll need message queue in Postgresql
# https://chatgpt.com/c/67ae12a0-5788-8006-a68b-bc5533ca37fd

## Start from the official PostgreSQL image
#FROM postgres:16.4
#
## Install dependencies
#RUN apt-get update && apt-get install -y \
#    curl \
#    make \
#    gcc \
#    postgresql-server-dev-16 \
#    && rm -rf /var/lib/apt/lists/*
#
## Install pgxman (PostgreSQL extension manager)
#RUN curl -fsSL https://pgxman.io/install.sh | bash  # !!!!!! this doesn't exist. install from source: https://github.com/tembo-io/pgmq/blob/main/CONTRIBUTING.md#pgxn-installation
#
## Install pgmq
#RUN pgxman install pgmq
#
## Clean up
#RUN apt-get clean && rm -rf /var/lib/apt/lists/*
#
## Ensure the extension is available at runtime
#CMD ["postgres"]