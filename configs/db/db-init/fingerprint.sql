/*
    Using plural for table names to match API endpoints.
    ID prefixes:
        fing_       fingerprint
 */
create database fingerprints;
\c fingerprints;

SET TIMEZONE='UTC';

DO $$
BEGIN
--     IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = 'appio_fingerprint') THEN
--         CREATE USER appio_app WITH PASSWORD 'pass_fingerprint';
--     END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname='appio_id') THEN
        CREATE DOMAIN appio_id AS TEXT CHECK (VALUE ~ '^[a-z_]{2,10}_[0-9a-hjkmnp-tv-z]{26}$');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname='non_empty_text') THEN
        CREATE DOMAIN non_empty_text AS TEXT NOT NULL CHECK (VALUE <> '');
    END IF;
END $$;

CREATE TABLE IF NOT EXISTS fingerprints
(
    id                  appio_id NOT NULL PRIMARY KEY,    -- <PERSON><PERSON><PERSON> prefixed with fing_
    service_id          appio_id NOT NULL,
    created_at          TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC'),
    customer_user_id    non_empty_text,
    ip                  inet NOT NULL,
    user_agent          non_empty_text,
    screen_resolution   non_empty_text,
    language            non_empty_text,
    time_offset         INTEGER NOT NULL,
    data                JSONB NOT NULL      -- raw data
);
CREATE INDEX idx_fingerprints_match ON fingerprints (ip, user_agent, screen_resolution, language, time_offset, created_at DESC);
-- no service_id foreign key on purpose for demo_svc_*


--------- DATA ---------- DATA ---------- DATA ---------- DATA ---------- DATA ---------- DATA ---------- DATA ---------


-- to test matching
-- INSERT INTO fingerprints (id, service_id, customer_user_id, ip, user_agent, screen_resolution, language, time_offset, data) VALUES
--     (
--         'fing_00000000000000000000000000',
--         'svc_00000000000000000000000000',
--         'demo123:<EMAIL>',
--         '::ffff:127.0.0.1',
--         'Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148',
--         '1792x1120',
--         'en-GB',
--         0,
--         '{"user_agent":"Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1 Mobile/15E148 Safari/604.1", "screen_resolution":"1792x1120", "language":"en-GB", "time_offset":0}'
--     );