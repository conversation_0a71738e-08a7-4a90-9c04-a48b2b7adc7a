services:
  appio_db:
    image: postgres:16.4
    container_name: appio_db
    environment:
      POSTGRES_USER: appio_api
      POSTGRES_PASSWORD: pass_api
      POSTGRES_DB: appio
      TZ: "UTC"  # Set timezone to UTC
    ports:
      - "5432:5432"  # Allow connection to Postgres from localhost
    volumes:
      - appio_db_data:/var/lib/postgresql/data  # Persist data
      - ./db-init:/docker-entrypoint-initdb.d  # Mount sql scripts for auto initialization
    networks:
      - appio_network
    command: [ "postgres", "-c", "listen_addresses=*" ]  # Allow connections from any IP

networks:
  appio_network:

volumes:
  appio_db_data: