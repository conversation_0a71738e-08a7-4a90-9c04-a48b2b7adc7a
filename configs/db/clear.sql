delete from widgets where service_id like 'demo_svc_%';
delete from notification_deliveries nd where nd.notification_id in (select id from notifications where service_id like 'demo_svc_%');
delete from notifications where service_id like 'demo_svc_%';

-- device id's needed to delete devices
SELECT string_agg(quote_literal(ds.device_id), ', ') as ids
FROM dvc_svc ds
WHERE ds.service_id IN (
    SELECT id FROM services WHERE id LIKE 'demo_svc_%'
);

delete from dvc_svc ds where ds.service_id in (select id from services where id like 'demo_svc_%');

-- use id's from above
delete from devices where id in ( ... );
delete from services where id like 'demo_svc_%';
