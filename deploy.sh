#!/bin/sh

# Check if at least one argument was provided
if [ "$#" -eq 0 ]; then
  echo "\n🔴 Missing commit message 🔴\n"
  exit 1
fi

commit_message="$1"

if [ -z "$commit_message" ]; then
  echo "\n🔴 Commit message is empty 🔴\n"
  exit 1
fi

echo "\n🟡 Running tests... 🟡\n"
./test.sh || { echo "🔴 Tests failed 🔴\n"; exit 1; }

# If all passes, push to git
git add -A
git commit -m"$commit_message"
git push

echo "\n🟢 Deployed successfully 🟢\n"