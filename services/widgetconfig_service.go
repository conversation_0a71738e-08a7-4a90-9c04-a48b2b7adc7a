package services

import (
	"api.appio.so/models"
	"api.appio.so/tmp"
	"fmt"
	"go.uber.org/zap"
	"math/rand"
	"strconv"
	"strings"
)

// WidgetConfigServiceInterface defines the interface for widget config service operations
type WidgetConfigServiceInterface interface {
	ParseWidgetConfig(platform models.Platform, widget *models.Widget) (*models.WidgetConfig, error)
}

type WidgetConfigService struct {
	logger *zap.Logger
}

func NewWidgetConfigService(logger *zap.Logger) *WidgetConfigService {
	return &WidgetConfigService{
		logger: logger,
	}
}

func (s *WidgetConfigService) ParseWidgetConfig(platform models.Platform, widget *models.Widget) (*models.WidgetConfig, error) {

	// TODO: transform widgets to widgetConfigs: merge data to template

	// NOTE: using hacky way to store demo template params as comma separated string. e.g.: ring,red,yellow,purple
	parts := strings.Split(widget.Template, ",")

	var config, name string
	if parts[0] == "number" {
		if platform == models.PlatformAndroid {
			config = tmp.AndroidNumberConfig
		} else {
			config = tmp.IOSNumberConfig
		}
		name = "Number"
	} else if parts[0] == "ring" {
		if platform == models.PlatformAndroid {
			config = tmp.AndroidRingConfig
		} else {
			config = tmp.IOSRingConfig
		}
		name = "Ring"
	} else {
		if platform == models.PlatformAndroid {
			config = tmp.AndroidTmpConfig
		} else {
			config = tmp.IOSTmpConfig
		}
		name = "Debug"
	}

	var textColor, backgroundColor, tintColor string
	if len(parts) > 1 {
		textColor = parts[1]
	}
	if len(parts) > 2 {
		backgroundColor = parts[2]
	}
	if len(parts) > 3 {
		tintColor = parts[3]
	}

	if widget.Source.Type == "static" {
		config = strings.ReplaceAll(config, "{{VALUE}}", string(widget.Source.Data))
	}
	if widget.Source.Type == "random" {
		config = strings.ReplaceAll(config, "{{VALUE}}", strconv.Itoa(rand.Intn(99)))
	}

	if textColor != "" && textColor != "primary" {
		config = strings.ReplaceAll(config, `"color": "primary"`, fmt.Sprintf(`"color": "%s"`, textColor))
	}
	if backgroundColor != "" && backgroundColor != "primary" {
		config = strings.ReplaceAll(config, `"background": "clear"`, fmt.Sprintf(`"background": "%s"`, backgroundColor))
	}
	if tintColor != "" && tintColor != "primary" {
		config = strings.ReplaceAll(config, `"tint": "primary"`, fmt.Sprintf(`"tint": "%s"`, tintColor))
	}

	// clean up whitespace to save bandwidth
	//re := regexp.MustCompile(`\s+`)
	//config = re.ReplaceAllString(config, " ")

	widgetConfig := models.WidgetConfig{
		ID:        widget.ID,
		ServiceID: widget.ServiceID,
		Name:      name,
		Config:    config,
	}

	return &widgetConfig, nil
}
