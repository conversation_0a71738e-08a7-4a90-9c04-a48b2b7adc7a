package services

import (
	"context"
	"errors"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func setupLastSeenAtService(t *testing.T) (*LastSeenAtService, *mocks.MockDeviceRepositoryInterface, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)
	logger := zaptest.NewLogger(t)
	service := NewLastSeenAtService(mockRepo, logger)
	return service, mockRepo, ctrl
}

func TestNewLastSeenAtService(t *testing.T) {
	t.Run("Creates service correctly", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockDeviceRepositoryInterface(ctrl)
		logger := zaptest.NewLogger(t)

		service := NewLastSeenAtService(mockRepo, logger)

		assert.NotNil(t, service)
		assert.Equal(t, mockRepo, service.repository)
		assert.Equal(t, logger, service.logger)
	})

	t.Run("Creates service with nil parameters", func(t *testing.T) {
		service := NewLastSeenAtService(nil, nil)

		assert.NotNil(t, service)
		assert.Nil(t, service.repository)
		assert.Nil(t, service.logger)
	})
}

func TestLastSeenAtService_Update(t *testing.T) {
	ctx := context.Background()
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")

	t.Run("Success - updates last seen at", func(t *testing.T) {
		service, mockRepo, ctrl := setupLastSeenAtService(t)
		defer ctrl.Finish()

		mockRepo.EXPECT().
			UpdateLastSeenAt(ctx, dvcID).
			Return(nil)

		err := service.Update(ctx, dvcID)

		assert.NoError(t, err)
	})

	t.Run("Repository error - returns internal error", func(t *testing.T) {
		service, mockRepo, ctrl := setupLastSeenAtService(t)
		defer ctrl.Finish()

		repoError := errors.New("database connection failed")
		mockRepo.EXPECT().
			UpdateLastSeenAt(ctx, dvcID).
			Return(repoError)

		err := service.Update(ctx, dvcID)

		assert.Equal(t, pkg.ErrInternal, err)
	})

	t.Run("Nil device ID - still calls repository", func(t *testing.T) {
		service, mockRepo, ctrl := setupLastSeenAtService(t)
		defer ctrl.Finish()

		var nilDvcID *appioid.ID = nil
		mockRepo.EXPECT().
			UpdateLastSeenAt(ctx, nilDvcID).
			Return(nil)

		err := service.Update(ctx, nilDvcID)

		assert.NoError(t, err)
	})

	t.Run("Repository timeout error", func(t *testing.T) {
		service, mockRepo, ctrl := setupLastSeenAtService(t)
		defer ctrl.Finish()

		timeoutError := errors.New("context deadline exceeded")
		mockRepo.EXPECT().
			UpdateLastSeenAt(ctx, dvcID).
			Return(timeoutError)

		err := service.Update(ctx, dvcID)

		assert.Equal(t, pkg.ErrInternal, err)
	})
}

func TestLastSeenAtService_Interface(t *testing.T) {
	t.Run("Implements LastSeenAtServiceInterface", func(t *testing.T) {
		service, _, ctrl := setupLastSeenAtService(t)
		defer ctrl.Finish()

		// Verify that LastSeenAtService implements the interface
		var _ LastSeenAtServiceInterface = service
	})
}
