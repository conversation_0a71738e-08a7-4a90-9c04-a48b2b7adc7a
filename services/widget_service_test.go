package services

import (
	"api.appio.so/models"
	"api.appio.so/internal/mocks"
	"api.appio.so/pkg"
	"context"
	"errors"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap"
	"testing"
)

func TestWidgetService_FindByID(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	wgtID := appioid.MustParse("wgt_00000000000000000000000001")

	tests := []struct {
		name           string
		mockResponse   *models.Widget
		mockError      error
		expectedResult *models.Widget
		expectedError  error
	}{
		{
			name: "Success - widget found",
			mockResponse: &models.Widget{
				ID:        wgtID,
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "number,#000000,#FFFFFF,#FF0000",
				},
			},
			mockError: nil,
			expectedResult: &models.Widget{
				ID:        wgtID,
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "number,#000000,#FFFFFF,#FF0000",
				},
			},
			expectedError: nil,
		},
		{
			name:           "Not found - pgx.ErrNoRows",
			mockResponse:   nil,
			mockError:      pgx.ErrNoRows,
			expectedResult: nil,
			expectedError:  pkg.ErrNotFound,
		},
		{
			name:           "Repository error",
			mockResponse:   nil,
			mockError:      errors.New("database error"),
			expectedResult: nil,
			expectedError:  pkg.ErrInternal,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
			service := NewWidgetService(mockRepo, logger)

			mockRepo.EXPECT().
				FindByID(ctx, svcID, wgtID).
				Return(tt.mockResponse, tt.mockError)

			result, err := service.FindByID(ctx, svcID, wgtID)

			if tt.expectedError != nil {
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}

func TestWidgetService_List(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - widgets found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		expectedWidgets := []models.Widget{
			{
				ID:        appioid.MustParse("wgt_00000000000000000000000001"),
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "number,#000000,#FFFFFF,#FF0000",
				},
			},
			{
				ID:        appioid.MustParse("wgt_00000000000000000000000002"),
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "ring,#000000,#FFFFFF,#00FF00",
				},
			},
		}

		mockRepo.EXPECT().
			List(ctx, svcID).
			Return(expectedWidgets, nil)

		result, err := service.List(ctx, svcID)

		assert.NoError(t, err)
		assert.Equal(t, expectedWidgets, result)
	})

	t.Run("Success - empty list", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		expectedWidgets := []models.Widget{}

		mockRepo.EXPECT().
			List(ctx, svcID).
			Return(expectedWidgets, nil)

		result, err := service.List(ctx, svcID)

		assert.NoError(t, err)
		assert.Equal(t, expectedWidgets, result)
	})

	t.Run("Repository error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		mockRepo.EXPECT().
			List(ctx, svcID).
			Return(nil, errors.New("database error"))

		result, err := service.List(ctx, svcID)

		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})
}

func TestWidgetService_Create(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - widget created", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		widgetReq := models.WidgetRequest{
			Template: "number,#000000,#FFFFFF,#FF0000",
		}

		mockRepo.EXPECT().
			Create(ctx, svcID, gomock.Any(), widgetReq).
			Return(nil)

		result, err := service.Create(ctx, svcID, widgetReq)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.True(t, result.String()[:3] == models.WidgetPrefix)
	})

	t.Run("Repository error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		widgetReq := models.WidgetRequest{
			Template: "number,#000000,#FFFFFF,#FF0000",
		}

		mockRepo.EXPECT().
			Create(ctx, svcID, gomock.Any(), widgetReq).
			Return(errors.New("database error"))

		result, err := service.Create(ctx, svcID, widgetReq)

		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})
}

func TestWidgetService_Update(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	wgtID := appioid.MustParse("wgt_00000000000000000000000001")

	t.Run("Success - widget updated", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		widgetReq := models.WidgetRequest{
			Template: "ring,#000000,#FFFFFF,#00FF00",
		}

		mockRepo.EXPECT().
			Update(ctx, svcID, wgtID, widgetReq).
			Return(true, nil)

		err := service.Update(ctx, svcID, wgtID, widgetReq)

		assert.NoError(t, err)
	})

	t.Run("Widget not found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		widgetReq := models.WidgetRequest{
			Template: "ring,#000000,#FFFFFF,#00FF00",
		}

		mockRepo.EXPECT().
			Update(ctx, svcID, wgtID, widgetReq).
			Return(false, nil) // Not found

		err := service.Update(ctx, svcID, wgtID, widgetReq)

		assert.Equal(t, pkg.ErrNotFound, err)
	})

	t.Run("Repository error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		widgetReq := models.WidgetRequest{
			Template: "ring,#000000,#FFFFFF,#00FF00",
		}

		mockRepo.EXPECT().
			Update(ctx, svcID, wgtID, widgetReq).
			Return(false, errors.New("database error"))

		err := service.Update(ctx, svcID, wgtID, widgetReq)

		assert.Equal(t, pkg.ErrInternal, err)
	})
}

func TestWidgetService_Delete(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	wgtID := appioid.MustParse("wgt_00000000000000000000000001")

	t.Run("Success - widget deleted", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		mockRepo.EXPECT().
			Delete(ctx, svcID, wgtID).
			Return(true, nil)

		err := service.Delete(ctx, svcID, wgtID)

		assert.NoError(t, err)
	})

	t.Run("Widget not found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		mockRepo.EXPECT().
			Delete(ctx, svcID, wgtID).
			Return(false, nil) // Not found

		err := service.Delete(ctx, svcID, wgtID)

		assert.Equal(t, pkg.ErrNotFound, err)
	})

	t.Run("Repository error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		service := NewWidgetService(mockRepo, logger)

		mockRepo.EXPECT().
			Delete(ctx, svcID, wgtID).
			Return(false, errors.New("database error"))

		err := service.Delete(ctx, svcID, wgtID)

		assert.Equal(t, pkg.ErrInternal, err)
	})
}
