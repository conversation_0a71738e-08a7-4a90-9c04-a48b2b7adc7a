package services

import (
	"api.appio.so/models"
	"api.appio.so/pkg"
	"api.appio.so/repositories"
	"context"
	"errors"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"
)

// WidgetServiceInterface defines the interface for widget service operations
type WidgetServiceInterface interface {
	FindByID(ctx context.Context, svcID, wgtID *appioid.ID) (*models.Widget, error)
	List(ctx context.Context, svcID *appioid.ID) ([]models.Widget, error)
	Create(ctx context.Context, svcID *appioid.ID, wgtReq models.WidgetRequest) (*appioid.ID, error)
	Update(ctx context.Context, svcID, wgtID *appioid.ID, wgtReq models.WidgetRequest) error
	Delete(ctx context.Context, svcID, wgtID *appioid.ID) error
}

type WidgetService struct {
	repository repositories.WidgetRepositoryInterface
	logger     *zap.Logger
}

func NewWidgetService(repository repositories.WidgetRepositoryInterface, logger *zap.Logger) *WidgetService {
	return &WidgetService{
		repository: repository,
		logger:     logger,
	}
}

func (s *WidgetService) FindByID(ctx context.Context, svcID, wgtID *appioid.ID) (*models.Widget, error) {
	widget, err := s.repository.FindByID(ctx, svcID, wgtID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, pkg.ErrNotFound
		}
		s.logger.Error("finding widget", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return widget, nil
}

func (s *WidgetService) List(ctx context.Context, svcID *appioid.ID) ([]models.Widget, error) {
	widgets, err := s.repository.List(ctx, svcID)
	if err != nil {
		s.logger.Error("listing widgets", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return widgets, nil
}

func (s *WidgetService) Create(ctx context.Context, svcID *appioid.ID, wgtReq models.WidgetRequest) (*appioid.ID, error) {
	wgtID, err := appioid.New(models.WidgetPrefix)
	if err != nil {
		return nil, pkg.ErrInternal
	}

	err = s.repository.Create(ctx, svcID, wgtID, wgtReq)
	if err != nil {
		s.logger.Error("creating widget", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return wgtID, nil
}

func (s *WidgetService) Update(ctx context.Context, svcID, wgtID *appioid.ID, wgtReq models.WidgetRequest) error {
	ok, err := s.repository.Update(ctx, svcID, wgtID, wgtReq)
	if err != nil {
		s.logger.Error("updating widget", zap.Error(err))
		return pkg.ErrInternal
	}
	if !ok {
		return pkg.ErrNotFound
	}
	return nil
}

func (s *WidgetService) Delete(ctx context.Context, svcID, wgtID *appioid.ID) error {
	ok, err := s.repository.Delete(ctx, svcID, wgtID)
	if err != nil {
		s.logger.Error("deleting widget", zap.Error(err))
		return pkg.ErrInternal
	}
	if !ok {
		return pkg.ErrNotFound
	}
	return nil
}
