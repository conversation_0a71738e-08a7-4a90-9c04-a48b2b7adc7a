package services

import (
	"context"
	"net"
	"strings"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/models"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func setupFeedbackService(t *testing.T) (*FeedbackService, *mocks.MockFeedbackRepositoryInterface, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockRepo := mocks.NewMockFeedbackRepositoryInterface(ctrl)
	logger := zaptest.NewLogger(t)
	service := NewFeedbackService(mockRepo, logger)
	return service, mockRepo, ctrl
}

func TestFeedbackService_CreateFeedback(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")
	message := "This is test feedback"
	version := "1.2.3"

	t.Run("Success", func(t *testing.T) {
		service, mockRepo, ctrl := setupFeedbackService(t)
		defer ctrl.Finish()

		ip := net.ParseIP("***********")
		mockRepo.EXPECT().
			Create(gomock.Any(), gomock.Any(), models.PlatformIOS, version, ip, svcID, dvcID, message).
			Return(nil)

		fdbID, err := service.CreateFeedback(context.Background(), models.PlatformIOS, version, ip, svcID, dvcID, message)

		assert.NoError(t, err)
		assert.NotNil(t, fdbID)
		assert.True(t, strings.HasPrefix(fdbID.String(), models.FeedbackPrefix))
	})

	t.Run("SuccessWithNullIDs", func(t *testing.T) {
		service, mockRepo, ctrl := setupFeedbackService(t)
		defer ctrl.Finish()

		ip := net.ParseIP("********")
		mockRepo.EXPECT().
			Create(gomock.Any(), gomock.Any(), models.PlatformAndroid, version, ip, (*appioid.ID)(nil), (*appioid.ID)(nil), message).
			Return(nil)

		fdbID, err := service.CreateFeedback(context.Background(), models.PlatformAndroid, version, ip, nil, nil, message)

		assert.NoError(t, err)
		assert.NotNil(t, fdbID)
		assert.True(t, strings.HasPrefix(fdbID.String(), models.FeedbackPrefix))
	})

	t.Run("SuccessWithMixedNullIDs", func(t *testing.T) {
		service, mockRepo, ctrl := setupFeedbackService(t)
		defer ctrl.Finish()

		ip := net.ParseIP("**********")
		mockRepo.EXPECT().
			Create(gomock.Any(), gomock.Any(), models.PlatformIOS, version, ip, svcID, (*appioid.ID)(nil), message).
			Return(nil)

		fdbID, err := service.CreateFeedback(context.Background(), models.PlatformIOS, version, ip, svcID, nil, message)

		assert.NoError(t, err)
		assert.NotNil(t, fdbID)
		assert.True(t, strings.HasPrefix(fdbID.String(), models.FeedbackPrefix))
	})

	t.Run("RepositoryError", func(t *testing.T) {
		service, mockRepo, ctrl := setupFeedbackService(t)
		defer ctrl.Finish()

		expectedErr := assert.AnError

		ip := net.ParseIP("***********")
		mockRepo.EXPECT().
			Create(gomock.Any(), gomock.Any(), models.PlatformIOS, version, ip, svcID, dvcID, message).
			Return(expectedErr)

		fdbID, err := service.CreateFeedback(context.Background(), models.PlatformIOS, version, ip, svcID, dvcID, message)

		assert.Error(t, err)
		assert.Nil(t, fdbID)
		assert.Equal(t, expectedErr, err)
	})
}
