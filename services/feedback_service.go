package services

import (
	"api.appio.so/models"
	"api.appio.so/pkg"
	"context"
	"net"

	"api.appio.so/repositories"
	"github.com/appio-so/go-appioid"
	"go.uber.org/zap"
)

// FeedbackServiceInterface defines the interface for feedback service operations
type FeedbackServiceInterface interface {
	CreateFeedback(ctx context.Context, platform models.Platform, version string, ip net.IP, svcID *appioid.ID, dvcID *appioid.ID, message string) (*appioid.ID, error)
}

type FeedbackService struct {
	feedbackRepository repositories.FeedbackRepositoryInterface
	logger             *zap.Logger
}

func NewFeedbackService(feedbackRepository repositories.FeedbackRepositoryInterface, logger *zap.Logger) *FeedbackService {
	return &FeedbackService{
		feedbackRepository: feedbackRepository,
		logger:             logger,
	}
}

func (s *FeedbackService) CreateFeedback(ctx context.Context, platform models.Platform, version string, ip net.IP, svcID *appioid.ID, dvcID *appioid.ID, message string) (*appioid.ID, error) {
	fdbID, err := appioid.New(models.FeedbackPrefix)
	if err != nil {
		return nil, pkg.ErrInternal
	}

	err = s.feedbackRepository.Create(ctx, fdbID, platform, version, ip, svcID, dvcID, message)
	if err != nil {
		s.logger.Error("creating feedback", zap.Error(err))
		return nil, err
	}
	return fdbID, nil
}
