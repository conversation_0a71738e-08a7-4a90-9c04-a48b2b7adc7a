package services

import (
	"context"
	"strings"

	"api.appio.so/models"
	"api.appio.so/pkg/roles"
	"go.uber.org/zap"
)

// JWTServiceInterface defines the interface for JWT service operations
type JWTServiceInterface interface {
	IsValidJWT(token string) bool
	ParseJWT(ctx context.Context, token string) (usrID *models.User, role roles.Role, err error)
}

type JWTService struct {
	//repository repositories.UserRepository
	logger *zap.Logger
}

func NewJWTService( /*repository repositories.UserRepository,*/ logger *zap.Logger) *JWTService {
	return &JWTService{
		//repository: repository,
		logger: logger,
	}
}

// Quick and light validation
func (s *JWTService) IsValidJWT(token string) bool {
	parts := strings.Split(token, ".")
	return len(parts) == 3
}

func (s *JWTService) ParseJWT(ctx context.Context, token string) (usrID *models.User, role roles.Role, err error) {
	// TODO: JWT auth

	//user, err := s.authClient.GetUser(fmt.Sprintf("Bearer %s", token))
	//if err != nil {
	//	return nil, roles.Unknown, err
	//}
	//
	//// TODO: JWT store user data to DB ? and return our user
	//fmt.Printf("user: %#v\n", user)
	//b, _ := json.MarshalIndent(user, "", "  ")
	//fmt.Println(string(b))

	return nil, roles.Dashboard, nil
}
