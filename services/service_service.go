package services

import (
	"context"
	"errors"

	"api.appio.so/models"

	"api.appio.so/pkg"
	"api.appio.so/repositories"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"go.uber.org/zap"
)

// ServiceServiceInterface defines the interface for ServiceService operations
type ServiceServiceInterface interface {
	FindByID(ctx context.Context, svcID *appioid.ID) (*models.Service, error)
	FindByIDWithWidgets(ctx context.Context, svcID *appioid.ID) (*models.ServiceWithWidgets, error)
	FindByIDWithWidgetConfigs(ctx context.Context, platform models.Platform, svcID *appioid.ID) (*models.ServiceWithWidgetConfigs, error)
	ListByUser(ctx context.Context, usrID *appioid.ID) ([]models.Service, error)
	ListByDeviceWithWidgetConfigs(ctx context.Context, platform models.Platform, dvcID *appioid.ID) ([]models.ServiceWithWidgetConfigs, error)
	Create(ctx context.Context, orgID *appioid.ID, svcReq models.ServiceRequest, customPrefix string) (*appioid.ID, error)
	Update(ctx context.Context, svcID *appioid.ID, svcReq models.ServiceRequest) error
}

type ServiceService struct {
	serviceRepository   repositories.ServiceRepositoryInterface
	widgetRepository    repositories.WidgetRepositoryInterface
	widgetConfigService *WidgetConfigService
	logger              *zap.Logger
}

func NewServiceService(serviceRepository repositories.ServiceRepositoryInterface, widgetRepository repositories.WidgetRepositoryInterface, widgetConfigService *WidgetConfigService, logger *zap.Logger) *ServiceService {
	return &ServiceService{
		serviceRepository:   serviceRepository,
		widgetRepository:    widgetRepository,
		widgetConfigService: widgetConfigService,
		logger:              logger,
	}
}

func (s *ServiceService) FindByID(ctx context.Context, svcID *appioid.ID) (*models.Service, error) {
	service, err := s.serviceRepository.FindByID(ctx, svcID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, pkg.ErrNotFound
		}

		s.logger.Error("finding service", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	// Nil check for consistency of error returns across methods
	if service == nil {
		return nil, pkg.ErrNotFound
	}
	return service, nil
}

func (s *ServiceService) FindByIDWithWidgets(ctx context.Context, svcID *appioid.ID) (*models.ServiceWithWidgets, error) {
	service, err := s.serviceRepository.FindByID(ctx, svcID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, pkg.ErrNotFound
		}

		s.logger.Error("finding service with widgets", zap.Error(err))
		return nil, pkg.ErrInternal
	}

	// Prevents widgets lookup
	if service == nil {
		return nil, pkg.ErrNotFound
	}

	var widgets = make([]models.Widget, 0)
	widgets, err = s.widgetRepository.List(ctx, service.ID)
	if err != nil {
		s.logger.Error("listing widgets", zap.Error(err))
		return nil, pkg.ErrInternal
	}

	return &models.ServiceWithWidgets{
		Service: service,
		Widgets: widgets,
	}, nil
}

func (s *ServiceService) FindByIDWithWidgetConfigs(ctx context.Context, platform models.Platform, svcID *appioid.ID) (*models.ServiceWithWidgetConfigs, error) {
	service, err := s.serviceRepository.FindByID(ctx, svcID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, pkg.ErrNotFound
		}

		s.logger.Error("finding service with widgets", zap.Error(err))
		return nil, pkg.ErrInternal
	}

	// Prevents widgets lookup
	if service == nil {
		return nil, pkg.ErrNotFound
	}

	serviceWithWidgetConfigs, err := s.makeServiceWithWidgetConfigs(ctx, platform, service)
	if err != nil {
		return nil, pkg.ErrInternal
	}
	return serviceWithWidgetConfigs, nil
}

func (s *ServiceService) ListByUser(ctx context.Context, usrID *appioid.ID) ([]models.Service, error) {
	services, err := s.serviceRepository.ListByUserID(ctx, usrID)
	if err != nil {
		s.logger.Error("listing services by user", zap.String("user_id", usrID.String()), zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return services, nil
}

func (s *ServiceService) ListByDeviceWithWidgetConfigs(ctx context.Context, platform models.Platform, dvcID *appioid.ID) ([]models.ServiceWithWidgetConfigs, error) {
	services, err := s.serviceRepository.ListByDeviceID(ctx, dvcID)
	if err != nil {
		s.logger.Error("listing services with widgets", zap.Error(err))
		return nil, pkg.ErrInternal
	}

	servicesWithWidgetConfigs := make([]models.ServiceWithWidgetConfigs, 0, len(services))
	for _, service := range services {
		serviceWithWidgetConfigs, err := s.makeServiceWithWidgetConfigs(ctx, platform, &service)
		if err != nil {
			s.logger.Error("Creating serviceWithWidgetConfigs", zap.Error(err))
			return nil, pkg.ErrInternal
		}
		servicesWithWidgetConfigs = append(servicesWithWidgetConfigs, *serviceWithWidgetConfigs)
	}

	return servicesWithWidgetConfigs, nil
}

func (s *ServiceService) Create(ctx context.Context, orgID *appioid.ID, svcReq models.ServiceRequest, customPrefix string) (*appioid.ID, error) {
	prefix := models.ServicePrefix
	if customPrefix != "" {
		prefix = customPrefix
	}
	svcID, err := appioid.New(prefix)
	if err != nil {
		return nil, pkg.ErrInternal
	}
	err = s.serviceRepository.Create(ctx, svcID, orgID, svcReq)
	if err != nil {
		s.logger.Error("creating service", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return svcID, nil
}

func (s *ServiceService) Update(ctx context.Context, svcID *appioid.ID, svcReq models.ServiceRequest) error {
	ok, err := s.serviceRepository.Update(ctx, svcID, svcReq)
	if err != nil {
		s.logger.Error("updating service", zap.Error(err))
		return pkg.ErrInternal
	}
	if !ok {
		return pkg.ErrNotFound
	}
	return nil
}

// --------------------------------------------------------------------------------------------------------------------

func (s *ServiceService) makeServiceWithWidgetConfigs(ctx context.Context, platform models.Platform, service *models.Service) (*models.ServiceWithWidgetConfigs, error) {
	var widgets = make([]models.Widget, 0)
	widgets, err := s.widgetRepository.List(ctx, service.ID)
	if err != nil {
		s.logger.Error("listing widgets", zap.Error(err))
		return nil, pkg.ErrInternal
	}

	var widgetConfigs []models.WidgetConfig
	for _, widget := range widgets {
		widgetConfig, err := s.widgetConfigService.ParseWidgetConfig(platform, &widget)
		if err != nil {
			s.logger.Error("creating widget config", zap.String("widget_id", widget.ID.String()), zap.Error(err))
			return nil, pkg.ErrInternal
		}
		widgetConfigs = append(widgetConfigs, *widgetConfig)
	}

	return &models.ServiceWithWidgetConfigs{
		Service:       service,
		WidgetConfigs: widgetConfigs,
	}, nil
}
