package services

import (
	"context"

	"api.appio.so/pkg"
	"api.appio.so/repositories"
	"github.com/appio-so/go-appioid"
	"go.uber.org/zap"
)

type LastSeenAtServiceInterface interface {
	Update(ctx context.Context, dvcID *appioid.ID) error
}

type LastSeenAtService struct {
	repository repositories.DeviceRepositoryInterface
	logger     *zap.Logger
}

func NewLastSeenAtService(repository repositories.DeviceRepositoryInterface, logger *zap.Logger) *LastSeenAtService {
	return &LastSeenAtService{
		repository: repository,
		logger:     logger,
	}
}

func (s *LastSeenAtService) Update(ctx context.Context, dvcID *appioid.ID) error {
	err := s.repository.UpdateLastSeenAt(ctx, dvcID)
	if err != nil {
		s.logger.Warn("failed to update device last_seen_at",
			zap.String("device_id", dvcID.String()),
			zap.Error(err))
		return pkg.ErrInternal
	}
	return nil
}
