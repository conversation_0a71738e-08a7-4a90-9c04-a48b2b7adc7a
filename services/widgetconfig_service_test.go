package services

import (
	"api.appio.so/models"
	"encoding/json"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"strings"
	"testing"
)

func TestWidgetConfigService_ParseWidgetConfig(t *testing.T) {
	logger := zap.NewNop()
	service := NewWidgetConfigService(logger)
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	wgtID := appioid.MustParse("wgt_00000000000000000000000001")

	tests := []struct {
		name           string
		widget         *models.Widget
		expectedName   string
		expectedConfig func(string) bool // Function to validate config content
		expectError    bool
	}{
		{
			name: "Number widget with default colors",
			widget: &models.Widget{
				ID:        wgtID,
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "number",
					Source: models.WidgetSource{
						Type: "static",
						Data: json.RawMessage(`"42"`),
					},
				},
			},
			expectedName: "Number",
			expectedConfig: func(config string) bool {
				return strings.Contains(config, "42") // Just check that the value is replaced
			},
			expectError: false,
		},
		{
			name: "Ring widget with custom colors",
			widget: &models.Widget{
				ID:        wgtID,
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "ring,#FF0000,#00FF00,#0000FF",
					Source: models.WidgetSource{
						Type: "static",
						Data: json.RawMessage(`"75"`),
					},
				},
			},
			expectedName: "Ring",
			expectedConfig: func(config string) bool {
				return strings.Contains(config, "75") &&
					strings.Contains(config, "#FF0000") &&
					strings.Contains(config, "#00FF00")
				// Note: tint color (#0000FF) might not be in the config if there's no tint property
			},
			expectError: false,
		},
		{
			name: "Random source widget",
			widget: &models.Widget{
				ID:        wgtID,
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "number",
					Source: models.WidgetSource{
						Type: "random",
						Data: json.RawMessage(`{}`),
					},
				},
			},
			expectedName: "Number",
			expectedConfig: func(config string) bool {
				// Should contain a random number (0-98)
				return !strings.Contains(config, "{{VALUE}}")
			},
			expectError: false,
		},
		{
			name: "Unknown widget type",
			widget: &models.Widget{
				ID:        wgtID,
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "unknown_type",
					Source: models.WidgetSource{
						Type: "static",
						Data: json.RawMessage(`"test"`),
					},
				},
			},
			expectedName: "Debug",
			expectedConfig: func(config string) bool {
				return strings.Contains(config, "test")
			},
			expectError: false,
		},
		{
			name: "Widget with primary colors (should not replace)",
			widget: &models.Widget{
				ID:        wgtID,
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "number,primary,primary,primary",
					Source: models.WidgetSource{
						Type: "static",
						Data: json.RawMessage(`"100"`),
					},
				},
			},
			expectedName: "Number",
			expectedConfig: func(config string) bool {
				return strings.Contains(config, "100") &&
					strings.Contains(config, `"color": "primary"`)
			},
			expectError: false,
		},
		{
			name: "Widget with empty template parts",
			widget: &models.Widget{
				ID:        wgtID,
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "number,,,",
					Source: models.WidgetSource{
						Type: "static",
						Data: json.RawMessage(`"50"`),
					},
				},
			},
			expectedName: "Number",
			expectedConfig: func(config string) bool {
				return strings.Contains(config, "50")
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := service.ParseWidgetConfig(models.PlatformIOS, tt.widget)

			if tt.expectError {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.widget.ID, result.ID)
				assert.Equal(t, tt.widget.ServiceID, result.ServiceID)
				assert.Equal(t, tt.expectedName, result.Name)
				assert.True(t, tt.expectedConfig(result.Config), "Config validation failed for: %s", result.Config)
			}
		})
	}
}

func TestWidgetConfigService_ParseWidgetConfig_EdgeCases(t *testing.T) {
	logger := zap.NewNop()
	service := NewWidgetConfigService(logger)
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	wgtID := appioid.MustParse("wgt_00000000000000000000000001")

	t.Run("Nil widget", func(t *testing.T) {
		// This would panic in the current implementation, but testing the behavior
		defer func() {
			if r := recover(); r != nil {
				// Expected to panic with nil widget
				assert.NotNil(t, r)
			}
		}()

		_, err := service.ParseWidgetConfig(models.PlatformIOS, nil)
		// If we reach here, the function didn't panic
		assert.Error(t, err)
	})

	t.Run("Empty template", func(t *testing.T) {
		widget := &models.Widget{
			ID:        wgtID,
			ServiceID: svcID,
			WidgetRequest: models.WidgetRequest{
				Template: "",
				Source: models.WidgetSource{
					Type: "static",
					Data: json.RawMessage(`"test"`),
				},
			},
		}

		result, err := service.ParseWidgetConfig(models.PlatformIOS, widget)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "Debug", result.Name) // Should default to debug for unknown type
	})

	t.Run("Very long template with many parts", func(t *testing.T) {
		widget := &models.Widget{
			ID:        wgtID,
			ServiceID: svcID,
			WidgetRequest: models.WidgetRequest{
				Template: "number,#FF0000,#00FF00,#0000FF,extra,parts,ignored",
				Source: models.WidgetSource{
					Type: "static",
					Data: json.RawMessage(`"123"`),
				},
			},
		}

		result, err := service.ParseWidgetConfig(models.PlatformIOS, widget)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "Number", result.Name)
		assert.Contains(t, result.Config, "123")
		assert.Contains(t, result.Config, "#FF0000")
		assert.Contains(t, result.Config, "#00FF00")
		// Note: tint color might not be replaced if there's no tint property in the template
	})
}

func TestWidgetConfigService_PlatformSpecific(t *testing.T) {
	logger := zap.NewNop()
	service := NewWidgetConfigService(logger)
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	wgtID := appioid.MustParse("wgt_00000000000000000000000001")

	widget := &models.Widget{
		ID:        wgtID,
		ServiceID: svcID,
		WidgetRequest: models.WidgetRequest{
			Template: "number",
			Source: models.WidgetSource{
				Type: "static",
				Data: json.RawMessage(`"42"`),
			},
		},
	}

	t.Run("iOS platform returns iOS config", func(t *testing.T) {
		result, err := service.ParseWidgetConfig(models.PlatformIOS, widget)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "Number", result.Name)
		assert.Contains(t, result.Config, "supportedFamilies") // iOS-specific
		assert.Contains(t, result.Config, "systemSmall")       // iOS-specific
	})

	t.Run("Android platform returns Android config", func(t *testing.T) {
		result, err := service.ParseWidgetConfig(models.PlatformAndroid, widget)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "Number", result.Name)
		assert.Contains(t, result.Config, "width")  // Android-specific
		assert.Contains(t, result.Config, "height") // Android-specific
		assert.NotContains(t, result.Config, "supportedFamilies") // Should not contain iOS-specific
	})
}

func TestWidgetConfigService_Constructor(t *testing.T) {
	t.Run("NewWidgetConfigService creates service correctly", func(t *testing.T) {
		logger := zap.NewNop()
		service := NewWidgetConfigService(logger)

		assert.NotNil(t, service)
		assert.Equal(t, logger, service.logger)
	})

	t.Run("NewWidgetConfigService with nil logger", func(t *testing.T) {
		service := NewWidgetConfigService(nil)

		assert.NotNil(t, service)
		assert.Nil(t, service.logger)
	})
}
