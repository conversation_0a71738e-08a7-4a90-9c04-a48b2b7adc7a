package services

import (
	"api.appio.so/models"
	"api.appio.so/pkg"
	"api.appio.so/pkg/database"
	"api.appio.so/repositories"
	"context"
	"errors"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
	"strings"
)

// DeviceServiceInterface defines the interface for device service operations
type DeviceServiceInterface interface {
	FindByID(ctx context.Context, svcID, dvcID *appioid.ID) (*models.DeviceResponse, error)
	FindByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]models.DeviceResponse, error)
	List(ctx context.Context, svcID *appioid.ID) ([]models.DeviceResponse, error)
	CreateAndLink(ctx context.Context, svcID *appioid.ID, dvcReq models.DeviceCreateRequest) (*appioid.ID, error)
	LinkWithService(ctx context.Context, svcID, dvcID *appioid.ID, dvcLinkReq models.DeviceLinkServiceRequest) error
	Update(ctx context.Context, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) error
	Deactivate(ctx context.Context, svcID, dvcID *appioid.ID) error
	DeactivateByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]appioid.ID, error)
}

type DeviceService struct {
	deviceRepository     repositories.DeviceRepositoryInterface
	dvcSvcRepository     repositories.DeviceServiceRepositoryInterface
	deviceNameRepository repositories.DeviceNameRepositoryInterface
	DB                   *pgxpool.Pool
	logger               *zap.Logger
	TxExecutor           database.TxExecutorFunc
}

func NewDeviceService(deviceRepository repositories.DeviceRepositoryInterface, dvcSvcRepository repositories.DeviceServiceRepositoryInterface, deviceNameRepository repositories.DeviceNameRepositoryInterface, db *pgxpool.Pool, logger *zap.Logger) *DeviceService {
	return &DeviceService{
		deviceRepository:     deviceRepository,
		dvcSvcRepository:     dvcSvcRepository,
		deviceNameRepository: deviceNameRepository,
		DB:                   db,
		logger:               logger,
		TxExecutor:           database.ExecuteTx,
	}
}

func (s *DeviceService) FindByID(ctx context.Context, svcID, dvcID *appioid.ID) (*models.DeviceResponse, error) {
	dvc, err := s.deviceRepository.FindByID(ctx, svcID, dvcID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, pkg.ErrNotFound
		}

		s.logger.Error("finding device", zap.Error(err))
		return nil, pkg.ErrInternal
	}

	return &models.DeviceResponse{DeviceRecord: *dvc}, nil
}

func (s *DeviceService) FindByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]models.DeviceResponse, error) {
	devices, err := s.deviceRepository.FindByCustomerUserID(ctx, svcID, customerUserID)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, pkg.ErrNotFound
		}

		s.logger.Error("finding devices by customerUserID", zap.Error(err))
		return nil, pkg.ErrInternal
	}

	responses := make([]models.DeviceResponse, len(devices))
	for i, dvc := range devices {
		responses[i] = models.DeviceResponse{DeviceRecord: dvc}
	}
	return responses, nil
}

func (s *DeviceService) List(ctx context.Context, svcID *appioid.ID) ([]models.DeviceResponse, error) {
	devices, err := s.deviceRepository.List(ctx, svcID)
	if err != nil {
		s.logger.Error("listing devices", zap.Error(err))
		return nil, pkg.ErrInternal
	}

	responses := make([]models.DeviceResponse, len(devices))
	for i, dvc := range devices {
		responses[i] = models.DeviceResponse{DeviceRecord: dvc}
	}
	return responses, nil
}

// Create Device and DeviceService
func (s *DeviceService) CreateAndLink(ctx context.Context, svcID *appioid.ID, dvcReq models.DeviceCreateRequest) (*appioid.ID, error) {
	dvcID, err := appioid.New(models.DevicePrefix)
	if err != nil {
		return nil, pkg.ErrInternal
	}

	dvcReq.MarketingName = s.lookupMarketingName(ctx, dvcReq.Platform, dvcReq.Model, dvcReq.DeviceIdentifier)

	err = s.TxExecutor(ctx, s.DB, s.logger, func(tx pgx.Tx) error {
		if err := s.deviceRepository.CreateTx(tx, ctx, dvcID, dvcReq); err != nil {
			s.logger.Error("creating device", zap.Error(err))
			return pkg.ErrInternal
		}

		dvcSvcId, err := appioid.New(models.DeviceServicePrefix)
		if err != nil {
			return pkg.ErrInternal
		}

		if err := s.dvcSvcRepository.CreateTx(tx, ctx, dvcSvcId, dvcID, svcID, dvcReq.CustomerUserID); err != nil {
			s.logger.Error("creating device-service", zap.Error(err))
			return pkg.ErrInternal
		}

		return nil
	})

	if err != nil {
		s.logger.Error("creating device and linking with service", zap.Error(err))
		return nil, pkg.ErrInternal
	}

	return dvcID, nil
}

func (s *DeviceService) LinkWithService(ctx context.Context, svcID, dvcID *appioid.ID, dvcLinkReq models.DeviceLinkServiceRequest) error {
	dvcSvcId, err := appioid.New(models.DeviceServicePrefix)
	if err != nil {
		return pkg.ErrInternal
	}
	err = s.dvcSvcRepository.Create(ctx, dvcSvcId, dvcID, svcID, dvcLinkReq.CustomerUserID)
	if err != nil {
		// Duplicate constraint error
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) && pgErr.Code == "23505" && pgErr.ConstraintName == "uniq_active_dvc_svc" {
			s.logger.Info("device is already linked to service", zap.Error(err))
			return pkg.ErrMatch
		}

		s.logger.Error("linking device with service", zap.Error(err))
		return pkg.ErrInternal
	}
	return nil
}

func (s *DeviceService) Update(ctx context.Context, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) error {
	ok, err := s.deviceRepository.Update(ctx, dvcID, dvcUpdateReq)
	if err != nil {
		s.logger.Error("updating device", zap.Error(err))
		return pkg.ErrInternal
	}
	if !ok {
		return pkg.ErrNotFound
	}

	return nil
}

func (s *DeviceService) Deactivate(ctx context.Context, svcID, dvcID *appioid.ID) error {
	ok, err := s.dvcSvcRepository.Deactivate(ctx, svcID, dvcID)
	if err != nil {
		s.logger.Error("deactivating device", zap.Error(err))
		return pkg.ErrInternal
	}
	if !ok {
		return pkg.ErrNotFound
	}
	return nil
}

func (s *DeviceService) DeactivateByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]appioid.ID, error) {
	dvcIDs, err := s.dvcSvcRepository.DeactivateByCustomerUserID(ctx, svcID, customerUserID)
	if err != nil {
		s.logger.Error("deactivating devices by customerUserID", zap.Error(err))
		return nil, pkg.ErrInternal
	}
	return dvcIDs, nil
}

// --------------------------------------------------------------------------------------------------------------------

func (s *DeviceService) lookupMarketingName(ctx context.Context, platform models.Platform, model, deviceIdentifier string) string {
	var brand, modelName string

	if platform == models.PlatformIOS {
		brand = "apple"
		modelName = deviceIdentifier
	} else if platform == models.PlatformAndroid {
		parts := strings.Split(model, "┼")
		if len(parts) == 2 {
			brand = parts[0]
			modelName = parts[1]
		}
	}

	if brand == "" || modelName == "" {
		return ""
	}

	marketingName, err := s.deviceNameRepository.FindMarketingName(ctx, brand, modelName)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return ""
		}

		s.logger.Warn("failed to lookup marketing name", zap.Error(err))
		return ""
	}

	return marketingName
}
