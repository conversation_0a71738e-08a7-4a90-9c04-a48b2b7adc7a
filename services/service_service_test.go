package services

import (
	"api.appio.so/internal/mocks"
	"api.appio.so/models"
	"api.appio.so/pkg"
	"context"
	"encoding/json"
	"errors"
	"github.com/appio-so/go-appioid"
	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap"
	"testing"
)

func TestServiceService_FindByID(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	tests := []struct {
		name           string
		mockResponse   *models.Service
		mockError      error
		expectedResult *models.Service
		expectedError  error
	}{
		{
			name: "Success - service found",
			mockResponse: &models.Service{
				ID: svcID,
				ServiceRequest: models.ServiceRequest{
					Title:           "Test Service",
					TextColor:       "#000000",
					BackgroundColor: "#FFFFFF",
					AccentColor:     "#ff0000",
				},
			},
			mockError: nil,
			expectedResult: &models.Service{
				ID: svcID,
				ServiceRequest: models.ServiceRequest{
					Title:           "Test Service",
					TextColor:       "#000000",
					BackgroundColor: "#FFFFFF",
					AccentColor:     "#ff0000",
				},
			},
			expectedError: nil,
		},
		{
			name:           "Not found - pgx.ErrNoRows",
			mockResponse:   nil,
			mockError:      pgx.ErrNoRows,
			expectedResult: nil,
			expectedError:  pkg.ErrNotFound,
		},
		{
			name:           "Repository returns nil service",
			mockResponse:   nil,
			mockError:      nil,
			expectedResult: nil,
			expectedError:  pkg.ErrNotFound,
		},
		{
			name:           "Repository error",
			mockResponse:   nil,
			mockError:      errors.New("database error"),
			expectedResult: nil,
			expectedError:  pkg.ErrInternal,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
			mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
			mockWidgetConfigService := &WidgetConfigService{} // Simple struct, no mocking needed

			service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

			mockServiceRepo.EXPECT().
				FindByID(ctx, svcID).
				Return(tt.mockResponse, tt.mockError)

			result, err := service.FindByID(ctx, svcID)

			if tt.expectedError != nil {
				assert.Equal(t, tt.expectedError, err)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}

func TestServiceService_FindByIDWithWidgets(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - service with widgets found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		expectedService := &models.Service{
			ID: svcID,
			ServiceRequest: models.ServiceRequest{
				Title:           "Test Service",
				TextColor:       "#000000",
				BackgroundColor: "#FFFFFF",
				AccentColor:     "#ff0000",
			},
		}
		expectedWidgets := []models.Widget{
			{
				ID:        appioid.MustParse("wgt_00000000000000000000000001"),
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "number,#000000,#FFFFFF,#FF0000",
				},
			},
			{
				ID:        appioid.MustParse("wgt_00000000000000000000000002"),
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "ring,#000000,#FFFFFF,#00FF00",
				},
			},
		}

		mockServiceRepo.EXPECT().
			FindByID(ctx, svcID).
			Return(expectedService, nil)

		mockWidgetRepo.EXPECT().
			List(ctx, svcID).
			Return(expectedWidgets, nil)

		result, err := service.FindByIDWithWidgets(ctx, svcID)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, expectedService, result.Service)
		assert.Equal(t, expectedWidgets, result.Widgets)
	})

	t.Run("Service not found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		mockServiceRepo.EXPECT().
			FindByID(ctx, svcID).
			Return(nil, pgx.ErrNoRows)

		result, err := service.FindByIDWithWidgets(ctx, svcID)

		assert.Equal(t, pkg.ErrNotFound, err)
		assert.Nil(t, result)
	})

	t.Run("Service found but widgets list fails", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		expectedService := &models.Service{
			ID: svcID,
			ServiceRequest: models.ServiceRequest{
				Title:           "Test Service",
				TextColor:       "#000000",
				BackgroundColor: "#FFFFFF",
				AccentColor:     "#ff0000",
			},
		}

		mockServiceRepo.EXPECT().
			FindByID(ctx, svcID).
			Return(expectedService, nil)

		mockWidgetRepo.EXPECT().
			List(ctx, svcID).
			Return(nil, errors.New("widget list error"))

		result, err := service.FindByIDWithWidgets(ctx, svcID)

		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})

	t.Run("Service repository returns nil service", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		mockServiceRepo.EXPECT().
			FindByID(ctx, svcID).
			Return(nil, nil) // No error but nil service

		result, err := service.FindByIDWithWidgets(ctx, svcID)

		assert.Equal(t, pkg.ErrNotFound, err)
		assert.Nil(t, result)
	})
}

func TestServiceService_Create(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()

	t.Run("Success - create with default prefix", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		serviceReq := models.ServiceRequest{
			Title:           "Test Service",
			TextColor:       "#FF0000",
			BackgroundColor: "#00FF00",
			AccentColor:     "#ff0000",
		}

		orgID := appioid.MustParse("org_00000000000000000000000000")

		mockServiceRepo.EXPECT().
			Create(ctx, gomock.Any(), orgID, serviceReq).
			Return(nil)

		result, err := service.Create(ctx, orgID, serviceReq, "")

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.True(t, result.String()[:3] == models.ServicePrefix)
	})

	t.Run("Success - create with custom prefix", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		serviceReq := models.ServiceRequest{
			Title:           "Test Service",
			TextColor:       "#FF0000",
			BackgroundColor: "#00FF00",
			AccentColor:     "#ff0000",
		}
		customPrefix := "custom"

		orgID := appioid.MustParse("org_00000000000000000000000000")

		mockServiceRepo.EXPECT().
			Create(ctx, gomock.Any(), orgID, serviceReq).
			Return(nil)

		result, err := service.Create(ctx, orgID, serviceReq, customPrefix)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.True(t, result.String()[:6] == customPrefix)
	})

	t.Run("Repository error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		serviceReq := models.ServiceRequest{
			Title:           "Test Service",
			TextColor:       "#FF0000",
			BackgroundColor: "#00FF00",
			AccentColor:     "#ff0000",
		}

		orgID := appioid.MustParse("org_00000000000000000000000000")

		mockServiceRepo.EXPECT().
			Create(ctx, gomock.Any(), orgID, serviceReq).
			Return(errors.New("database error"))

		result, err := service.Create(ctx, orgID, serviceReq, "")

		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})
}

func TestServiceService_Update(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - service updated", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		serviceReq := models.ServiceRequest{
			Title:           "Updated Service",
			TextColor:       "#0000FF",
			BackgroundColor: "#FFFF00",
			AccentColor:     "#ff0000",
		}

		mockServiceRepo.EXPECT().
			Update(ctx, svcID, serviceReq).
			Return(true, nil)

		err := service.Update(ctx, svcID, serviceReq)

		assert.NoError(t, err)
	})

	t.Run("Service not found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		serviceReq := models.ServiceRequest{
			Title:           "Updated Service",
			TextColor:       "#0000FF",
			BackgroundColor: "#FFFF00",
			AccentColor:     "#ff0000",
		}

		mockServiceRepo.EXPECT().
			Update(ctx, svcID, serviceReq).
			Return(false, nil) // Not found

		err := service.Update(ctx, svcID, serviceReq)

		assert.Equal(t, pkg.ErrNotFound, err)
	})

	t.Run("Repository error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		serviceReq := models.ServiceRequest{
			Title:           "Updated Service",
			TextColor:       "#0000FF",
			BackgroundColor: "#FFFF00",
			AccentColor:     "#ff0000",
		}

		mockServiceRepo.EXPECT().
			Update(ctx, svcID, serviceReq).
			Return(false, errors.New("database error"))

		err := service.Update(ctx, svcID, serviceReq)

		assert.Equal(t, pkg.ErrInternal, err)
	})
}

func TestServiceService_FindByIDWithWidgetConfigs(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - service with widget configs found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{logger: logger}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		expectedService := &models.Service{
			ID: svcID,
			ServiceRequest: models.ServiceRequest{
				Title:           "Test Service",
				TextColor:       "#000000",
				BackgroundColor: "#FFFFFF",
				AccentColor:     "#ff0000",
			},
		}
		expectedWidgets := []models.Widget{
			{
				ID:        appioid.MustParse("wgt_00000000000000000000000001"),
				ServiceID: svcID,
				WidgetRequest: models.WidgetRequest{
					Template: "number",
					Source: models.WidgetSource{
						Type: "static",
						Data: json.RawMessage(`"42"`),
					},
				},
			},
		}

		mockServiceRepo.EXPECT().
			FindByID(ctx, svcID).
			Return(expectedService, nil)

		mockWidgetRepo.EXPECT().
			List(ctx, svcID).
			Return(expectedWidgets, nil)

		result, err := service.FindByIDWithWidgetConfigs(ctx, models.PlatformIOS, svcID)

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, expectedService, result.Service)
		assert.Len(t, result.WidgetConfigs, 1)
		assert.Equal(t, expectedWidgets[0].ID, result.WidgetConfigs[0].ID)
	})

	t.Run("Service not found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		mockServiceRepo.EXPECT().
			FindByID(ctx, svcID).
			Return(nil, pgx.ErrNoRows)

		result, err := service.FindByIDWithWidgetConfigs(ctx, models.PlatformIOS, svcID)

		assert.Equal(t, pkg.ErrNotFound, err)
		assert.Nil(t, result)
	})

	t.Run("Widget list error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		expectedService := &models.Service{
			ID: svcID,
			ServiceRequest: models.ServiceRequest{
				Title:           "Test Service",
				TextColor:       "#000000",
				BackgroundColor: "#FFFFFF",
				AccentColor:     "#ff0000",
			},
		}

		mockServiceRepo.EXPECT().
			FindByID(ctx, svcID).
			Return(expectedService, nil)

		mockWidgetRepo.EXPECT().
			List(ctx, svcID).
			Return(nil, errors.New("widget list error"))

		result, err := service.FindByIDWithWidgetConfigs(ctx, models.PlatformIOS, svcID)

		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})
}

func TestServiceService_ListByDeviceWithWidgetConfigs(t *testing.T) {
	logger := zap.NewNop()
	ctx := context.Background()
	dvcID := appioid.MustParse("dvc_00000000000000000000000001")

	t.Run("Success - services with widget configs found", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{logger: logger}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		svcID1 := appioid.MustParse("svc_00000000000000000000000001")
		svcID2 := appioid.MustParse("svc_00000000000000000000000002")
		expectedServices := []models.Service{
			{
				ID: svcID1,
				ServiceRequest: models.ServiceRequest{
					Title:           "Service 1",
					TextColor:       "#FF0000",
					BackgroundColor: "#00FF00",
					AccentColor:     "#ff0000",
				},
			},
			{
				ID: svcID2,
				ServiceRequest: models.ServiceRequest{
					Title:           "Service 2",
					TextColor:       "#0000FF",
					BackgroundColor: "#FFFF00",
					AccentColor:     "#ff0000",
				},
			},
		}

		mockServiceRepo.EXPECT().
			ListByDeviceID(ctx, dvcID).
			Return(expectedServices, nil)

		// Mock widget lists for each service
		mockWidgetRepo.EXPECT().
			List(ctx, svcID1).
			Return([]models.Widget{}, nil)

		mockWidgetRepo.EXPECT().
			List(ctx, svcID2).
			Return([]models.Widget{}, nil)

		result, err := service.ListByDeviceWithWidgetConfigs(ctx, models.PlatformIOS, dvcID)

		assert.NoError(t, err)
		assert.Len(t, result, 2)
		assert.Equal(t, expectedServices[0].ID, result[0].Service.ID)
		assert.Equal(t, expectedServices[1].ID, result[1].Service.ID)
	})

	t.Run("Repository error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		mockServiceRepo := mocks.NewMockServiceRepositoryInterface(ctrl)
		mockWidgetRepo := mocks.NewMockWidgetRepositoryInterface(ctrl)
		mockWidgetConfigService := &WidgetConfigService{}

		service := NewServiceService(mockServiceRepo, mockWidgetRepo, mockWidgetConfigService, logger)

		mockServiceRepo.EXPECT().
			ListByDeviceID(ctx, dvcID).
			Return(nil, errors.New("database error"))

		result, err := service.ListByDeviceWithWidgetConfigs(ctx, models.PlatformIOS, dvcID)

		assert.Equal(t, pkg.ErrInternal, err)
		assert.Nil(t, result)
	})
}
