package main

import (
	"api.appio.so/cmd/cron/config"
	"api.appio.so/cmd/cron/device_names"
	"api.appio.so/cmd/cron/docs"
	"api.appio.so/cmd/cron/notifications"
	"api.appio.so/pkg"
	"api.appio.so/pkg/database"
	"context"
	"github.com/getsentry/sentry-go"
	"github.com/jackc/pgx/v5/pgxpool"
	"log"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/appio-so/go-zaplog"
	"github.com/go-co-op/gocron/v2"
	"go.uber.org/zap"
)

func main() {
	cfg, err := config.LoadConfig("config.cron")
	if err != nil {
		log.Fatalf("failed to load config: %v", err)
	}

	logger, err := zaplog.NewLogger(cfg.Server.LogLevel, cfg.Server.Env, cfg.Server.Name)
	if err != nil {
		log.Fatalf("failed to initialize logger: %v", err)
	}
	defer logger.Sync()

	logger = pkg.NewSentryLogger(logger, &cfg.Server, &cfg.Sentry)
	defer sentry.Flush(2 * time.Second)

	db, _, err := database.InitDatabase(context.Background(), &cfg.DB, logger)
	if err != nil {
		logger.Fatal("failed to init database", zap.Error(err))
	}
	defer db.Close()

	s, err := gocron.NewScheduler(
		gocron.WithLocation(time.UTC),
		gocron.WithGlobalJobOptions(
			gocron.WithSingletonMode(gocron.LimitModeReschedule),
		),
	)
	if err != nil {
		logger.Fatal("failed to create scheduler", zap.Error(err))
	}

	// Schedule jobs
	notificationQueuingJob(s, db, logger, 1*time.Second)
	notificationDeliveryJob(s, db, logger, cfg.IOS, cfg.Android, 1*time.Second)
	notificationDeliveryCleanupJob(s, db, logger, 10*time.Second)
	docsResetJob(s, db, logger, 15*time.Minute)
	deviceNamesImportJob(s, db, logger, cfg.IOS, cfg.Android, 24*time.Hour)

	s.Start()
	logger.Info("scheduler started, press Ctrl+C to stop ...")

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Block until a signal is received
	<-sigChan
	logger.Info("shutdown signal received. Shutting down scheduler...")

	err = s.Shutdown()
	if err != nil {
		logger.Fatal("failed to shutdown scheduler", zap.Error(err))
	}

	time.Sleep(2 * time.Second)
	logger.Info("graceful shutdown complete")
}

func deviceNamesImportJob(s gocron.Scheduler, db *pgxpool.Pool, logger *zap.Logger, iosCfg config.IOS, androidCfg config.Android, duration time.Duration) {
	deviceNamesImportJob, err := s.NewJob(
		gocron.DurationJob(duration),
		gocron.NewTask(
			device_names.ImportDeviceNames,
			db,
			iosCfg,
			androidCfg,
			logger,
		),
	)
	if err != nil {
		logger.Fatal("failed to create deviceNamesImportJob", zap.Error(err))
	} else {
		logger.Info("deviceNamesImportJob created", zap.Any("job_id", deviceNamesImportJob.ID()))
	}
}

func notificationQueuingJob(s gocron.Scheduler, db *pgxpool.Pool, logger *zap.Logger, duration time.Duration) {
	queueNotificationsJob, err := s.NewJob(
		gocron.DurationJob(duration),
		gocron.NewTask(
			notifications.Queue,
			db,
			logger,
		),
	)
	if err != nil {
		logger.Fatal("failed to create queueNotificationsJob", zap.Error(err))
	} else {
		logger.Info("queueNotificationsJob created", zap.Any("job_id", queueNotificationsJob.ID()))
	}
}

func notificationDeliveryJob(s gocron.Scheduler, db *pgxpool.Pool, logger *zap.Logger, iosCfg config.IOS, androidCfg config.Android, duration time.Duration) {
	deliverNotificationsJob, err := s.NewJob(
		gocron.DurationJob(duration),
		gocron.NewTask(
			notifications.Deliver,
			db,
			logger,
			iosCfg,
			androidCfg,
		),
	)
	if err != nil {
		logger.Fatal("failed to create deliverNotificationsJob", zap.Error(err))
	} else {
		logger.Info("deliverNotificationsJob created", zap.Any("job_id", deliverNotificationsJob.ID()))
	}
}

func notificationDeliveryCleanupJob(s gocron.Scheduler, db *pgxpool.Pool, logger *zap.Logger, duration time.Duration) {
	deliverCleanupNotificationsJob, err := s.NewJob(
		gocron.DurationJob(duration),
		gocron.NewTask(
			notifications.Cleanup,
			db,
			logger,
		),
	)
	if err != nil {
		logger.Fatal("failed to create notificationDeliveryCleanupJob", zap.Error(err))
	} else {
		logger.Info("notificationDeliveryCleanupJob created", zap.Any("job_id", deliverCleanupNotificationsJob.ID()))
	}
}

func docsResetJob(s gocron.Scheduler, db *pgxpool.Pool, logger *zap.Logger, duration time.Duration) {
	docsResetJob, err := s.NewJob(
		gocron.DurationJob(duration),
		gocron.NewTask(
			docs.Reset,
			db,
			logger,
		),
	)
	if err != nil {
		logger.Fatal("failed to create docsResetJob", zap.Error(err))
	} else {
		logger.Info("docsResetJob created", zap.Any("job_id", docsResetJob.ID()))
	}
}
