package notifications

import (
	"api.appio.so/cmd/cron/config"
	"api.appio.so/models"
	"api.appio.so/models/notification_status"
	"api.appio.so/models/notification_type"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
	"time"
)

var logger *zap.Logger

const (
	deliveryTimeout = 10 * time.Second
	// Maximum number of notifications to process per cron job run
	maxNotificationsPerBatch = 1000

	// Select all queued regardless of deactivated service or device. This will be checked during delivery
	selectDeliveryQuery = `
		SELECT
			nd.id, nd.notification_id, n.payload, n.type,
			s.id, s.title, s.logo_url,
			d.id, d.platform, d.device_token
		FROM notification_deliveries nd
		JOIN notifications n ON n.id=nd.notification_id
		JOIN devices d ON d.id=nd.device_id
		JOIN services s ON s.id=n.service_id
		WHERE nd.status=@status
		FOR UPDATE SKIP LOCKED
		LIMIT 1`

	selectDeviceNotificationsEnabledQuery = `
		SELECT EXISTS (
			SELECT 1 FROM devices
			WHERE id=@id
			  AND notifications_enabled = true
			  AND device_token IS NOT NULL
			  AND device_token <> ''
		)`

	updateDeliveryStatusQuery = `
		UPDATE notification_deliveries 
		SET status=@status, error_message=@err_msg, updated_at=NOW() 
		WHERE id=@id`

	updateDeviceNotificationsQuery = `
		UPDATE devices 
		SET notifications_enabled=false 
		WHERE id=@id`
)

type NotificationPayload struct {
	Title    *string `json:"title"`
	Message  *string `json:"message"`
	Link     *string `json:"link"`
	ImageURL *string `json:"image_url"`
}

type deliveryData struct {
	ID       string
	NotifID  string
	Payload  string
	Type     notification_type.Type
	SvcID    string
	Title    string
	LogoURL  string
	DeviceID string
	Platform models.Platform
	Token    *string
}

func Deliver(db *pgxpool.Pool, log *zap.Logger, iosCfg config.IOS, androidCfg config.Android) {
	logger = log // Set the package-level logger

	iosClient := NewIOSClient(iosCfg.KeyFile, iosCfg.KeyId, iosCfg.TeamId, iosCfg.Topic, iosCfg.ServerName, iosCfg.DefaultCategory)
	androidClient := NewAndroidClient(androidCfg.KeyFile, androidCfg.BaseURL)

	if err := deliverNotifications(db, iosClient, androidClient); err != nil {
		logger.Error("delivering notifications error", zap.Error(err))
	}
}

func deliverNotifications(db *pgxpool.Pool, iosClient *IOSClient, androidClient *AndroidClient) error {
	processedCount := 0
	for processedCount < maxNotificationsPerBatch {
		isNext, err := deliverNextNotification(db, iosClient, androidClient)
		if err != nil {
			return fmt.Errorf("process next delivery: %w", err)
		}
		if !isNext {
			break
		}
		processedCount++
	}
	return nil
}

func deliverNextNotification(db *pgxpool.Pool, iosClient *IOSClient, androidClient *AndroidClient) (bool, error) {
	ctx, cancel := context.WithTimeout(context.Background(), deliveryTimeout)
	defer cancel()

	tx, err := db.Begin(ctx)
	if err != nil {
		return false, fmt.Errorf("begin transaction: %w", err)
	}
	defer tx.Rollback(context.Background())

	// Set SQL deadline to prevent deadlocks (fallback)
	_, err = tx.Exec(ctx, fmt.Sprintf("SET LOCAL statement_timeout = '%dms'", deliveryTimeout.Milliseconds()))

	delivery, err := fetchNextDelivery(ctx, tx)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return false, nil
		}
		return false, fmt.Errorf("fetch delivery: %w", err)
	}

	if err := processDelivery(ctx, tx, delivery, iosClient, androidClient); err != nil {
		return false, fmt.Errorf("process delivery: %w", err)
	}

	if err := tx.Commit(ctx); err != nil {
		return false, fmt.Errorf("commit transaction: %w", err)
	}

	return true, nil
}

func fetchNextDelivery(ctx context.Context, tx pgx.Tx) (*deliveryData, error) {
	var delivery deliveryData
	args := pgx.NamedArgs{"status": notification_status.Queued}

	err := tx.QueryRow(ctx, selectDeliveryQuery, args).Scan(
		&delivery.ID, &delivery.NotifID, &delivery.Payload, &delivery.Type,
		&delivery.SvcID, &delivery.Title, &delivery.LogoURL,
		&delivery.DeviceID, &delivery.Platform, &delivery.Token,
	)
	if err != nil {
		return nil, err
	}
	return &delivery, nil
}

func processDelivery(ctx context.Context, tx pgx.Tx, d *deliveryData, iosClient *IOSClient, androidClient *AndroidClient) error {
	logger.Debug("delivering notification", zap.String("deliveryID", d.ID))

	// N2H: func isServiceDeactivated() is called from `queue.go`, it should be inside this file, so this job can eventually run standalone in lambda
	if isServiceDeactivated(ctx, tx, d.SvcID) {
		return markFailedDelivery(ctx, tx, d.ID, errors.New("service deactivated"))
	}
	if !isDeviceNotificationsEnabled(ctx, tx, d.DeviceID) {
		return markFailedDelivery(ctx, tx, d.ID, errors.New("notification disabled"))
	}

	if d.Platform == models.PlatformAndroid {
		return handleAndroidDelivery(ctx, tx, d, androidClient)
	} else if d.Platform == models.PlatformIOS {
		return handleIOSDelivery(ctx, tx, d, iosClient)
	}
	return nil
}

func handleAndroidDelivery(ctx context.Context, tx pgx.Tx, d *deliveryData, androidClient *AndroidClient) error {
	logger.Debug("delivering Android", zap.String("deliveryID", d.ID))

	if err := deliverAndroid(androidClient, d); err != nil {
		var androidError *AndroidError
		if errors.As(err, &androidError) {
			if err := processAndroidError(ctx, tx, androidError, d.DeviceID); err != nil {
				return fmt.Errorf("process Android error: %w", err)
			}
		}
		return markFailedDelivery(ctx, tx, d.ID, err)
	}
	return markDelivered(ctx, tx, d.ID)
}

func handleIOSDelivery(ctx context.Context, tx pgx.Tx, d *deliveryData, iosClient *IOSClient) error {
	logger.Debug("delivering iOS", zap.String("deliveryID", d.ID))

	if err := deliverIOS(iosClient, d); err != nil {
		var iosError *IOSError
		if errors.As(err, &iosError) {
			if err := processIOSError(ctx, tx, iosError, d.DeviceID); err != nil {
				return fmt.Errorf("process iOS error: %w", err)
			}
		}
		return markFailedDelivery(ctx, tx, d.ID, err)
	}
	return markDelivered(ctx, tx, d.ID)
}

func markFailedDelivery(ctx context.Context, tx pgx.Tx, deliveryID string, deliveryErr error) error {
	logger.Debug("failed to deliver", zap.String("deliveryID", deliveryID), zap.Error(deliveryErr))

	args := pgx.NamedArgs{
		"status":  notification_status.Failed,
		"err_msg": deliveryErr.Error(),
		"id":      deliveryID,
	}
	_, err := tx.Exec(ctx, updateDeliveryStatusQuery, args)
	return err
}

func markDelivered(ctx context.Context, tx pgx.Tx, deliveryID string) error {
	logger.Debug("delivered", zap.String("deliveryID", deliveryID))

	args := pgx.NamedArgs{
		"status":  notification_status.Completed,
		"err_msg": "",
		"id":      deliveryID,
	}
	_, err := tx.Exec(ctx, updateDeliveryStatusQuery, args)
	return err
}

func deliverIOS(iosClient *IOSClient, d *deliveryData) error {
	if d.Token == nil || *d.Token == "" {
		return NewIOSError(fmt.Errorf("empty apnToken"), ApnTokenErrorCode, nil)
	}
	token := *d.Token
	if iosClient.isValidToken(token) == false {
		return NewIOSError(fmt.Errorf("invalid apnToken"), ApnTokenErrorCode, nil)
	}

	if d.Type == notification_type.Background {
		return deliverIOSBackground(token, iosClient, d)
	}
	if d.Type == notification_type.Foreground {
		return deliverIOSForeground(token, iosClient, d)
	}
	return NewIOSError(fmt.Errorf("unknown notification type: %s", d.Type), UnknownErrorCode, nil)
}

func deliverIOSBackground(token string, iosClient *IOSClient, d *deliveryData) error {
	logger.Debug("delivering iOS background", zap.String("deliveryID", d.ID))

	// For now, this just triggers app to refresh service data
	return iosClient.BackgroundNotification(token, IOSData{
		NtfID: d.NotifID,
		SvcID: d.SvcID,
	})
}

func deliverIOSForeground(token string, iosClient *IOSClient, d *deliveryData) error {
	logger.Debug("delivering iOS foreground", zap.String("deliveryID", d.ID))

	var payload NotificationPayload
	if err := json.Unmarshal([]byte(d.Payload), &payload); err != nil {
		return fmt.Errorf("unmarshal payload: %w", err)
	}

	alert := IOSAlert{
		Title:    d.Title,
		Subtitle: getStringValue(payload.Title),
		Body:     getStringValue(payload.Message),
	}

	data := IOSData{
		NtfID:    d.NotifID,
		SvcID:    d.SvcID,
		LogoURL:  d.LogoURL,
		ImageURL: getStringValue(payload.ImageURL),
		Link:     getStringValue(payload.Link),
	}

	return iosClient.ForegroundNotification(token, d.SvcID, alert, data)
}

func getStringValue(ptr *string) string {
	if ptr != nil {
		return *ptr
	}
	return ""
}

func deliverAndroid(androidClient *AndroidClient, d *deliveryData) error {
	if d.Token == nil || *d.Token == "" {
		return NewAndroidError(fmt.Errorf("empty FCM token"), AndroidTokenErrorCode, nil)
	}
	token := *d.Token
	if androidClient.isValidToken(token) == false {
		return NewAndroidError(fmt.Errorf("invalid FCM token"), AndroidTokenErrorCode, nil)
	}

	if d.Type == notification_type.Background {
		return deliverAndroidBackground(token, androidClient, d)
	}
	if d.Type == notification_type.Foreground {
		return deliverAndroidForeground(token, androidClient, d)
	}
	return NewAndroidError(fmt.Errorf("unknown notification type: %s", d.Type), AndroidUnknownErrorCode, nil)
}

func deliverAndroidBackground(token string, androidClient *AndroidClient, d *deliveryData) error {
	logger.Debug("delivering Android background", zap.String("deliveryID", d.ID))

	return androidClient.BackgroundNotification(token, Data{
		NotificationID: d.NotifID,
		ServiceID:      d.SvcID,
	})
}

func deliverAndroidForeground(token string, androidClient *AndroidClient, d *deliveryData) error {
	logger.Debug("delivering Android foreground", zap.String("deliveryID", d.ID))

	var payload NotificationPayload
	if err := json.Unmarshal([]byte(d.Payload), &payload); err != nil {
		return fmt.Errorf("unmarshal payload: %w", err)
	}

	data := Data{
		NotificationID: d.NotifID,
		ServiceID:      d.SvcID,
		ServiceTitle:   d.Title,
		Title:          getStringValue(payload.Title),
		Body:           getStringValue(payload.Message),
		LogoURL:        d.LogoURL,
		ImageURL:       getStringValue(payload.ImageURL),
		Link:           getStringValue(payload.Link),
	}

	return androidClient.ForegroundNotification(token, data)
}

// Documentation: https://developer.apple.com/documentation/usernotifications/setting_up_a_remote_notification_server/handling_notification_responses_from_apns
func processIOSError(ctx context.Context, tx pgx.Tx, err *IOSError, deviceID string) error {
	logger.Debug("processing ios delivery error", zap.String("deviceID", deviceID), zap.String("error", err.Error()))

	// APN token error
	if err.Code == ApnTokenErrorCode {
		return deactivateDeviceNotifications(ctx, tx, deviceID)
	}

	switch err.ApnError.Reason {
	case "BadDeviceToken", "ExpiredProviderToken", "InvalidProviderToken", "ExpiredToken", "Unregistered":
		return deactivateDeviceNotifications(ctx, tx, deviceID)
	case "TooManyProviderTokenUpdates", "TooManyRequests":
		// N2H: we should wait before trying to deliver to this device again
		// TODO: implement rate limiting for these devices
		return nil
	default:
		return nil
	}
}

func deactivateDeviceNotifications(ctx context.Context, tx pgx.Tx, deviceID string) error {
	logger.Debug("deactivating device", zap.String("deviceID", deviceID))

	args := pgx.NamedArgs{"id": deviceID}
	_, err := tx.Exec(ctx, updateDeviceNotificationsQuery, args)
	return err
}

// Documentation: https://firebase.google.com/docs/cloud-messaging/send-message#rest-error
// Extra: PERMISSION_DENIED (SENDER_ID_MISMATCH), NOT_FOUND (UNREGISTERED), UNAUTHENTICATED (THIRD_PARTY_AUTH_ERROR)
func processAndroidError(ctx context.Context, tx pgx.Tx, err *AndroidError, deviceID string) error {
	logger.Debug("processing Android delivery error", zap.String("deviceID", deviceID), zap.String("error", err.Error()))

	// FCM token error
	if err.Code == AndroidTokenErrorCode {
		return deactivateDeviceNotifications(ctx, tx, deviceID)
	}

	switch err.FcmError.RawError.Status {
	case "NOT_FOUND", "UNREGISTERED":
		return deactivateDeviceNotifications(ctx, tx, deviceID)
	case "QUOTA_EXCEEDED", "UNAVAILABLE", "INTERNAL":
		// N2H: we should wait before trying to deliver to this device again
		// TODO: implement rate limiting for these devices
		return nil
	case "PERMISSION_DENIED", "SENDER_ID_MISMATCH", "UNSPECIFIED_ERROR", "INVALID_ARGUMENT", "THIRD_PARTY_AUTH_ERROR", "UNAUTHENTICATED":
		logger.Error("Android notification delivery error", zap.Error(err))
		return nil
	default:
		return nil
	}
}

func isDeviceNotificationsEnabled(ctx context.Context, tx pgx.Tx, deviceID string) bool {
	args := pgx.NamedArgs{"id": deviceID}
	var enabled bool
	if err := tx.QueryRow(ctx, selectDeviceNotificationsEnabledQuery, args).Scan(&enabled); err != nil {
		return false
	}
	return enabled
}
