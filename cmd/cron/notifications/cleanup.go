package notifications

import (
	"api.appio.so/models/notification_status"
	"context"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
	"time"
)

const CLEANUP_TIMEUOT = 10 * time.Second

func Cleanup(db *pgxpool.Pool, logger *zap.Logger) {
	err := cleanupNotifications(db)
	if err != nil {
		logger.Error("cleaning up notifications error", zap.Error(err))
	}
}

func cleanupNotifications(db *pgxpool.Pool) error {
	ctx, cancel := context.WithTimeout(context.Background(), CLEANUP_TIMEUOT)
	defer cancel()

	query := `UPDATE notifications n
				SET status=COALESCE(
    				(SELECT CASE
            				WHEN bool_or(nd.status=@failed1::queue_status)
              				THEN @failed2::queue_status
	            			ELSE @completed1::queue_status
        				END
    				    FROM notification_deliveries nd
        				WHERE nd.notification_id=n.id
    				),
    				@completed2::queue_status
				)
				WHERE n.status=@queued1::queue_status AND NOT EXISTS (
					SELECT 1
					FROM notification_deliveries nd
					WHERE nd.notification_id=n.id
						AND nd.status IN (@queued2::queue_status)
				  )`
	args := pgx.NamedArgs{
		"failed1":    notification_status.Failed,
		"failed2":    notification_status.Failed,
		"completed1": notification_status.Completed,
		"completed2": notification_status.Completed,
		"queued1":    notification_status.Queued,
		"queued2":    notification_status.Queued,
	}
	_, err := db.Exec(ctx, query, args)
	return err
}
