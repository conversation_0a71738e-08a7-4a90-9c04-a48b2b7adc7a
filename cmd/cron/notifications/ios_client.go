package notifications

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/net/http2"
	"io"
	"net/http"
	"os"
	"regexp"
	"sync"
	"time"
)

// Access token refresh interval - Apple recommends refreshing every 20 minutes, so we use 15
const IOSAccessTokenRefreshInterval = 15 * time.Minute

// Request timeout for individual APN requests
const APNRequestTimeout = 30 * time.Second

type httpClient interface {
	Do(*http.Request) (*http.Response, error)
}

type headers map[string]string

type IOSClient struct {
	auth       IOSAuth
	serverName string
	topic      string
	category   string
	client     httpClient

	// Access token caching
	accessToken    string
	tokenExpiresAt time.Time
	tokenMutex     sync.Mutex
}

type IOSAuth struct {
	file   string
	keyID  string
	teamID string
}

type IOSError struct {
	Err      error
	Code     int
	ApnError APNError
}

const ApnTokenErrorCode = -1
const UnknownErrorCode = -2

// Documentation: https://developer.apple.com/documentation/usernotifications/setting_up_a_remote_notification_server/handling_notification_responses_from_apns
type APNError struct {
	Success bool // decoded successfully from response body

	Reason string `json:"reason"`
	//Timestamp *int `json:"timestamp"` // optional. never used?
}

// Safe to output to end user
func (e *IOSError) Error() string {
	return fmt.Sprintf("code: %d, message: %s, error: %s", e.Code, e.ApnError.Reason, e.Err.Error())
}

func NewIOSError(err error, code int, message []byte) *IOSError {
	// decode message
	var apnError APNError
	if message == nil {
		apnError.Success = true
	} else {
		internalErr := json.Unmarshal(message, &apnError)
		if internalErr != nil {
			apnError.Success = false
			apnError.Reason = fmt.Sprintf("failed to decode ios error: %s", string(message))
		} else {
			apnError.Success = true
		}
	}

	return &IOSError{
		Err:      err,
		Code:     code,
		ApnError: apnError,
	}
}

func NewIOSClient(file, keyID, teamID, topic, serverName, defaultCategory string) *IOSClient {
	// Configure HTTP/2 transport optimized for APN persistent connections
	transport := &http2.Transport{
		// Allow multiple concurrent streams per connection (HTTP/2 feature)
		AllowHTTP: false, // Only HTTPS

		// Connection settings optimized for APN
		MaxHeaderListSize: 32 * 1024, // 32KB header limit

		// TLS configuration for secure HTTP/2 connections
		TLSClientConfig: &tls.Config{
			ServerName:         serverName,
			MinVersion:         tls.VersionTLS12,
			InsecureSkipVerify: false,
		},

		// Ping settings to keep connections alive
		ReadIdleTimeout:  30 * time.Second,
		PingTimeout:      15 * time.Second,
		WriteByteTimeout: 30 * time.Second,
	}

	return &IOSClient{
		auth: IOSAuth{
			file:   file,
			keyID:  keyID,
			teamID: teamID,
		},
		serverName: serverName,
		topic:      topic,
		category:   defaultCategory,
		client: &http.Client{
			Timeout:   0, // No client-level timeout to preserve persistent connections
			Transport: transport,
		},
	}
}

type FgIOSPayload struct {
	APS  FgAPSPayload `json:"aps"`
	Data IOSData      `json:"data"`
}

type BgIOSPayload struct {
	APS  BgAPSPayload `json:"aps"`
	Data IOSData      `json:"data"`
}

type FgAPSPayload struct {
	Alert          IOSAlert `json:"alert"`
	MutableContent int      `json:"mutable-content"` // 1
	ThreadID       string   `json:"thread-id"`       // service_id
	Category       string   `json:"category"`        // APPIO_SERVICE_CATEGORY
	Sound          string   `json:"sound"`           // default
}

type BgAPSPayload struct {
	ContentAvailable int `json:"content-available"`
}

type IOSAlert struct {
	Title    string `json:"title"`
	Subtitle string `json:"subtitle"`
	Body     string `json:"body"`
}

type IOSData struct {
	NtfID string `json:"notification_id"`
	SvcID string `json:"service_id"`

	// Fg: Optional
	LogoURL  string `json:"logo,omitempty"`
	ImageURL string `json:"image_url,omitempty"`
	Link     string `json:"link,omitempty"`
}

// Request docs: https://developer.apple.com/documentation/usernotifications/sending-notification-requests-to-apns
// Payload docs: https://developer.apple.com/documentation/usernotifications/generating-a-remote-notification
func (c *IOSClient) ForegroundNotification(apnToken, svcID string, alert IOSAlert, data IOSData) error {
	payload := FgIOSPayload{
		APS: FgAPSPayload{
			Alert:          alert,
			MutableContent: 1, // to trigger xcode's UNNotificationServiceExtension
			ThreadID:       svcID,
			Category:       c.category, // to trigger xcode's UNNotificationContentExtension, to show action buttons on push notification
			Sound:          "default",  // or any another sound on the device
		},
		Data: data,
	}
	d, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	code, body, err := c.request(fmt.Sprintf("/3/device/%s", apnToken), "POST", bytes.NewBuffer(d), headers{
		"apns-push-type": "alert",
	})
	if err != nil {
		return fmt.Errorf("api request failed: %w", err)
	}
	if code != http.StatusOK {
		return NewIOSError(fmt.Errorf("response code is not 200"), code, body)
	}
	// on success, the body should always be empty
	if len(body) > 0 {
		return NewIOSError(fmt.Errorf("response body is not empty"), code, body)
	}

	// TODO: store apns-id, apns-unique-id: https://developer.apple.com/documentation/usernotifications/handling-notification-responses-from-apns

	return nil
}

// Docs: https://developer.apple.com/documentation/usernotifications/pushing-background-updates-to-your-app
func (c *IOSClient) BackgroundNotification(apnToken string, data IOSData) error {
	payload := BgIOSPayload{
		APS: BgAPSPayload{
			ContentAvailable: 1,
		},
		Data: data,
	}
	d, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	code, body, err := c.request(fmt.Sprintf("/3/device/%s", apnToken), "POST", bytes.NewBuffer(d), headers{
		"apns-push-type": "background",
		"apns-priority":  "5",
	})
	if err != nil {
		return fmt.Errorf("api request failed: %w", err)
	}
	if code != http.StatusOK {
		return NewIOSError(err, code, body)
	}
	// on success, the body should always be empty
	if len(body) > 0 {
		return NewIOSError(fmt.Errorf("response body is not empty"), code, body)
	}

	// TODO: store apns-id, apns-unique-id: https://developer.apple.com/documentation/usernotifications/handling-notification-responses-from-apns

	return nil
}

func (c *IOSClient) request(uri, method string, data io.Reader, headers headers) (int, []byte, error) {
	// Create context with timeout for this specific request
	ctx, cancel := context.WithTimeout(context.Background(), APNRequestTimeout)
	defer cancel()

	url := fmt.Sprintf("https://%s%s", c.serverName, uri)
	req, err := http.NewRequestWithContext(ctx, method, url, data)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to create request: %w", err)
	}

	accessToken, err := c.getAccessToken()
	if err != nil {
		return 0, nil, fmt.Errorf("failed to get access token: %w", err)
	}

	req.Header.Set("content-type", "application/json")
	req.Header.Set("authorization", fmt.Sprintf("Bearer %s", accessToken))
	req.Header.Set("apns-topic", c.topic)

	for k, v := range headers {
		req.Header.Set(k, v)
	}

	resp, err := c.client.Do(req)
	if err != nil {
		return 0, nil, fmt.Errorf("http request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, nil, fmt.Errorf("failed to read API response: %w", err)
	}

	return resp.StatusCode, body, nil
}

// getAccessToken returns a cached access token or generates a new one if expired
func (c *IOSClient) getAccessToken() (string, error) {
	c.tokenMutex.Lock()
	defer c.tokenMutex.Unlock()

	if c.accessToken != "" && time.Now().Before(c.tokenExpiresAt) {
		return c.accessToken, nil
	}

	token, err := c.generateAccessToken()
	if err != nil {
		return "", err
	}

	c.accessToken = token
	c.tokenExpiresAt = time.Now().Add(IOSAccessTokenRefreshInterval)
	return token, nil
}

func (c *IOSClient) generateAccessToken() (string, error) {
	// Read .p8 key file
	keyData, err := os.ReadFile(c.auth.file)
	if err != nil {
		return "", fmt.Errorf("failed to read APNs key file: %w", err)
	}

	// Parse the ECDSA private key
	privateKey, err := jwt.ParseECPrivateKeyFromPEM(keyData)
	if err != nil {
		return "", fmt.Errorf("failed to parse APNs private key: %w", err)
	}

	// Create JWT token
	token := jwt.NewWithClaims(jwt.SigningMethodES256, jwt.MapClaims{
		"iss": c.auth.teamID,     // Team ID
		"iat": time.Now().Unix(), // Issued At
	})

	// Set "kid" (Key ID) in the header
	token.Header["kid"] = c.auth.keyID

	// Sign the token
	accessToken, err := token.SignedString(privateKey)
	if err != nil {
		return "", fmt.Errorf("failed to sign JWT: %w", err)
	}

	return accessToken, nil
}

func (c *IOSClient) isValidToken(token string) bool {
	var validToken = regexp.MustCompile(`^[0-9a-fA-F]{64}$`)
	return validToken.MatchString(token)
}
