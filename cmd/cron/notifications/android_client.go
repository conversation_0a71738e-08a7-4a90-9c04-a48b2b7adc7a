package notifications

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"golang.org/x/net/http2"
	"io"
	"net/http"
	"os"
	"regexp"
	"strings"
	"sync"
	"time"

	"golang.org/x/oauth2/google"
)

type AndroidClient struct {
	baseURL string
	client  httpClient
	keyFile string

	// Access token caching
	accessToken    string
	tokenExpiresAt time.Time
	tokenMutex     sync.Mutex
}

type AndroidError struct {
	Err      error
	Code     int
	FcmError FCMError
}

const (
	AndroidTokenErrorCode   = -1
	AndroidUnknownErrorCode = -2

	AndroidForeground string = "foreground"
	AndroidBackground string = "background"

	// Request timeout for individual FCM requests
	FCMRequestTimeout = 30 * time.Second

	// Access token refresh interval - Google tokens typically last 1 hour, refresh at 50 minutes
	AndroidAccessTokenRefreshInterval = 50 * time.Minute
)

// Documentation: https://firebase.google.com/docs/cloud-messaging/send-message#rest-error
type FCMError struct {
	Success bool // decoded successfully from response body

	RawError struct {
		Code    int              `json:"code"`
		Message string           `json:"message"`
		Status  string           `json:"status"`
		Details []map[string]any `json:"details,omitempty"`
	} `json:"error"`
}

// Safe to output to end user
func (e *AndroidError) Error() string {
	return fmt.Sprintf("code: %d, success: %v, message: %s, status: %s, error: %s", e.Code, e.FcmError.Success, e.FcmError.RawError.Message, e.FcmError.RawError.Status, e.Err.Error())
}

func NewAndroidError(err error, code int, message []byte) *AndroidError {
	var fcmError FCMError
	if message == nil {
		fcmError.Success = true
	} else {
		internalErr := json.Unmarshal(message, &fcmError)
		if internalErr != nil {
			fcmError.Success = false
			fcmError.RawError.Message = fmt.Sprintf("failed to decode android error: %s", string(message))
		} else {
			fcmError.Success = true
		}
	}

	return &AndroidError{
		Err:      err,
		Code:     code,
		FcmError: fcmError,
	}
}

func NewAndroidClient(keyFile, baseURL string) *AndroidClient {
	return &AndroidClient{
		baseURL: normalizeFirebaseURL(baseURL),
		keyFile: keyFile,
		client: &http.Client{
			Transport: &http2.Transport{},
			Timeout:   0, // No timeout to allow persistent connections
		},
	}
}

func normalizeFirebaseURL(baseURL string) string {
	return strings.TrimSuffix(baseURL, "/") + "/messages:send"
}

type AndroidPayload struct {
	Message AndroidMessage `json:"message"`
}

type AndroidMessage struct {
	Token   string  `json:"token"`
	Data    Data    `json:"data"`
	Android Android `json:"android"`
}

type Android struct {
	Priority string `json:"priority"` // "normal" or "high". for live activity we have to use "high"
}

type Data struct {
	NotificationID string `json:"notification_id"`
	ServiceID      string `json:"service_id"`
	Type           string `json:"type,omitempty"`

	// Foreground only
	ServiceTitle string `json:"service_title,omitempty"`
	Title        string `json:"title,omitempty"`
	Body         string `json:"body,omitempty"`
	LogoURL      string `json:"logo_url,omitempty"`
	ImageURL     string `json:"image_url,omitempty"`
	Link         string `json:"link,omitempty"`
}

func (c *AndroidClient) BackgroundNotification(token string, data Data) error {
	data.Type = AndroidBackground

	payload := AndroidPayload{
		Message: AndroidMessage{
			Token: token,
			Data:  data,
			Android: Android{
				Priority: "normal",
			},
		},
	}

	return c.sendMessage(payload)
}

func (c *AndroidClient) ForegroundNotification(token string, data Data) error {
	data.Type = AndroidForeground

	payload := AndroidPayload{
		Message: AndroidMessage{
			Token: token,
			Data:  data,
			Android: Android{
				Priority: "normal",
			},
		},
	}

	return c.sendMessage(payload)
}

func (c *AndroidClient) sendMessage(payload AndroidPayload) error {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal request: %w", err)
	}

	accessToken, err := c.getAccessToken()
	if err != nil {
		return fmt.Errorf("failed to get access token: %w", err)
	}

	// Create context with timeout for individual request
	ctx, cancel := context.WithTimeout(context.Background(), FCMRequestTimeout)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "POST", c.baseURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", accessToken))

	resp, err := c.client.Do(req)
	if err != nil {
		return fmt.Errorf("http request failed: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read API response: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return NewAndroidError(fmt.Errorf("response code is not 200"), resp.StatusCode, body)
	}

	// TODO: store message_id: https://firebase.google.com/docs/cloud-messaging/send-message#send_messages_to_specific_devices
	/* success response:
	{
	  "name": "projects/appio-so/messages/0:1719162738294321%1234567890abcdef"
	}
	*/

	return nil
}

// getAccessToken returns a cached access token or generates a new one if expired
func (c *AndroidClient) getAccessToken() (string, error) {
	c.tokenMutex.Lock()
	defer c.tokenMutex.Unlock()

	if c.accessToken != "" && time.Now().Before(c.tokenExpiresAt) {
		return c.accessToken, nil
	}

	token, err := c.generateAccessToken()
	if err != nil {
		return "", err
	}

	c.accessToken = token
	c.tokenExpiresAt = time.Now().Add(AndroidAccessTokenRefreshInterval)
	return token, nil
}

func (c *AndroidClient) generateAccessToken() (string, error) {
	keyData, err := os.ReadFile(c.keyFile)
	if err != nil {
		return "", fmt.Errorf("failed to read service account key file: %w", err)
	}

	config, err := google.JWTConfigFromJSON(keyData, "https://www.googleapis.com/auth/firebase.messaging")
	if err != nil {
		return "", fmt.Errorf("failed to parse service account key: %w", err)
	}

	token, err := config.TokenSource(context.Background()).Token()
	if err != nil {
		return "", fmt.Errorf("failed to get access token: %w", err)
	}

	return token.AccessToken, nil
}

func (c *AndroidClient) isValidToken(token string) bool {
	// NOTE: there is no pattern and token format changed over time
	var validToken = regexp.MustCompile(`^[a-zA-Z0-9\-_:]{140,}$`)
	return validToken.MatchString(token)
}
