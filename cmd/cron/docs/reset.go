package docs

import (
	"context"
	"time"

	"api.appio.so/models"
	"api.appio.so/models/notification_status"
	"api.appio.so/models/notification_type"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

const REST_TIMEUOT = 10 * time.Second
const DATA_PROTECTION_PERIOD = 30 * time.Minute
const DOC_SERVICE_ID = "svc_00dddddd000000ccccccssssss"
const DOC_ORGANIZATION_ID = "org_00000000000000000000000000"
const DOC_DEVICE_ID = "dvc_01jmpmh9fvxgyym44sqanjr9hs"
const DOC_DVC_SVC_ID = "dvcsvc_01jq47ppcr6se7709t7b3h4q9r"
const DOC_NOTIFICATION_ID = "ntf_01jmpmgb6my0s57c960q1s862v"
const DOC_NOTIFICATION_DELIVERY_ID = "ntfdlv_01jq48jnysm3ctpp64jv5fhhed"
const DOC_WIDGET_ID = "wgt_01jmpr3vt1008txv1jehgdeny7"

func Reset(db *pgxpool.Pool, logger *zap.Logger) {
	err := resetDocs(db)
	if err != nil {
		logger.Error("resetting docs error", zap.Error(err))
	}
}

func resetDocs(db *pgxpool.Pool) error {
	ctx, cancel := context.WithTimeout(context.Background(), REST_TIMEUOT)
	defer cancel()

	if err := resetService(db, ctx); err != nil {
		return err
	}
	if err := resetDevices(db, ctx); err != nil {
		return err
	}
	if err := resetNotifications(db, ctx); err != nil {
		return err
	}
	if err := resetWidgets(db, ctx); err != nil {
		return err
	}

	return nil
}

func resetService(db *pgxpool.Pool, ctx context.Context) error {
	query := `INSERT INTO services (
                      id,
                      organization_id,
                      title,
                      description,
                      banner_url,
                      logo_url,
                      url,
                      text_color,
                      background_color,
                      accent_color
				) VALUES (
						@id,
				        @organization_id,
			            @title,
			            @description,
			            @banner_url,
			            @logo_url,
				        @url,
				        @text_color,
				        @background_color,
				        @accent_color
				) ON CONFLICT (id) DO UPDATE
				    SET
				        title = EXCLUDED.title,
				        description = EXCLUDED.description,
				        banner_url = EXCLUDED.banner_url,
				        logo_url = EXCLUDED.logo_url,
				        url = EXCLUDED.url,
				        text_color = EXCLUDED.text_color,
				        background_color = EXCLUDED.background_color,
				        accent_color = EXCLUDED.accent_color
				        `
	args := pgx.NamedArgs{
		"id":               DOC_SERVICE_ID,
		"organization_id":  DOC_ORGANIZATION_ID,
		"title":            "Appio Docs",
		"description":      "Test Appio in action — download the app and experience its full potential.",
		"banner_url":       "https://cdn.appio.so/app/docs.appio.so/banner.jpg",
		"logo_url":         "https://cdn.appio.so/app/docs.appio.so/logo.png",
		"url":              "https://docs.appio.so",
		"text_color":       "#000000",
		"background_color": "#ffffff",
		"accent_color":     "#0066cc",
	}
	_, err := db.Exec(ctx, query, args)
	return err
}

func resetDevices(db *pgxpool.Pool, ctx context.Context) error {
	// delete extra dvc_svc connections older than 30 minutes
	query1 := `DELETE FROM dvc_svc
	           WHERE service_id=@service_id
	           AND device_id!=@device_id
	           AND created_at < NOW() AT TIME ZONE 'UTC' - INTERVAL '1 second' * @protection_period`
	args1 := pgx.NamedArgs{
		"service_id":        DOC_SERVICE_ID,
		"device_id":         DOC_DEVICE_ID,
		"protection_period": int(DATA_PROTECTION_PERIOD.Seconds()),
	}
	_, err := db.Exec(ctx, query1, args1)
	if err != nil {
		return err
	}

	// NOTE: we don't delete devices. since they indicate real interaction and we value that. ignoring internal testing

	// create or update a device
	insertDevice := `INSERT INTO devices (
                     id,
                     name,
                     platform,
                     os_version,
                     model,
                     device_token,
                     notifications_enabled,
                     device_identifier,
                     marketing_name
				) VALUES (
						@device_id,
			            @name,
			            @platform,
			            @os_version,
			            @model,
					  	@device_token,
				        @notifications_enabled,
				        @device_identifier,
				        @marketing_name
				) ON CONFLICT (id) DO UPDATE
				    SET
				        notifications_enabled = EXCLUDED.notifications_enabled`
	args2 := pgx.NamedArgs{
		"device_id":             DOC_DEVICE_ID,
		"name":                  "iPhone 13",
		"platform":              models.PlatformIOS,
		"os_version":            "18.3",
		"model":                 "iPhone",
		"device_token":          "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef",
		"notifications_enabled": true,
		"device_identifier":     "iPhone14,5",
		"marketing_name":        "iPhone 13",
	}
	_, err = db.Exec(ctx, insertDevice, args2)
	if err != nil {
		return err
	}

	// set or update dvc_svc to active
	query3 := `INSERT INTO dvc_svc (
                     id,
                     service_id,
                     device_id,
                     customer_user_id,
                     notifications_enabled
				) VALUES (
						@dvc_svc_id,
			            @service_id,
			            @device_id,
			            @customer_user_id,
			            @notifications_enabled
				) ON CONFLICT (id) DO UPDATE
				    SET
				        notifications_enabled = EXCLUDED.notifications_enabled,
				        deactivated_at = NULL`
	args3 := pgx.NamedArgs{
		"dvc_svc_id":            DOC_DVC_SVC_ID,
		"service_id":            DOC_SERVICE_ID,
		"device_id":             DOC_DEVICE_ID,
		"customer_user_id":      "docs-appio-so:<EMAIL>",
		"notifications_enabled": true,
	}
	_, err = db.Exec(ctx, query3, args3)
	return err
}

func resetNotifications(db *pgxpool.Pool, ctx context.Context) error {
	// delete extra notification deliveries for notifications older than 30 minutes
	query1 := `DELETE FROM notification_deliveries
       			USING notifications
				WHERE notifications.id=notification_deliveries.notification_id
  				AND notifications.service_id=@service_id
  				AND notification_deliveries.id!=@id
  				AND notifications.created_at < NOW() AT TIME ZONE 'UTC' - INTERVAL '1 second' * @protection_period`
	args1 := pgx.NamedArgs{
		"service_id":        DOC_SERVICE_ID,
		"id":                DOC_NOTIFICATION_DELIVERY_ID,
		"protection_period": int(DATA_PROTECTION_PERIOD.Seconds()),
	}
	_, err := db.Exec(ctx, query1, args1)
	if err != nil {
		return err
	}

	// delete extra notifications older than 30 minutes
	query0 := `DELETE FROM notifications
	           WHERE service_id=@service_id
	           AND id!=@id
	           AND created_at < NOW() AT TIME ZONE 'UTC' - INTERVAL '1 second' * @protection_period`
	args0 := pgx.NamedArgs{
		"service_id":        DOC_SERVICE_ID,
		"id":                DOC_NOTIFICATION_ID,
		"protection_period": int(DATA_PROTECTION_PERIOD.Seconds()),
	}
	_, err = db.Exec(ctx, query0, args0)
	if err != nil {
		return err
	}

	// create notification
	query2 := `INSERT INTO notifications (
                           id,
                           service_id,
                           type,
                           payload,
                           status
				) VALUES (
						@notification_id,
			            @service_id,
				        @type,
			            @payload,
			            @status
				) ON CONFLICT DO NOTHING`
	args2 := pgx.NamedArgs{
		"notification_id": DOC_NOTIFICATION_ID,
		"service_id":      DOC_SERVICE_ID,
		"type":            notification_type.Foreground,
		"payload":         `{"title": "Notification","message": "Hello from Appio Docs","link": "https://docs.appio.so","image_url": "https://cdn.appio.so/app/docs.appio.so/banner.jpg"}`,
		"status":          notification_status.Completed,
	}
	_, err = db.Exec(ctx, query2, args2)
	if err != nil {
		return err
	}

	// set or update notification_delivery to active
	query3 := `INSERT INTO notification_deliveries (
                                     id,
                                     notification_id,
                                     device_id,
                                     status,
                                     updated_at
				) VALUES (
				          @id,
			              @notification_id,
			              @device_id,
			              @status,
				          CURRENT_TIMESTAMP
				) ON CONFLICT DO NOTHING`
	args3 := pgx.NamedArgs{
		"id":              DOC_NOTIFICATION_DELIVERY_ID,
		"notification_id": DOC_NOTIFICATION_ID,
		"device_id":       DOC_DEVICE_ID,
		"status":          notification_status.Completed,
	}
	_, err = db.Exec(ctx, query3, args3)
	return err
}

func resetWidgets(db *pgxpool.Pool, ctx context.Context) error {
	// delete extra widgets older than 30 minutes
	query1 := `DELETE FROM widgets
	           WHERE service_id=@service_id
	           AND id!=@widget_id
	           AND created_at < NOW() AT TIME ZONE 'UTC' - INTERVAL '1 second' * @protection_period`
	args1 := pgx.NamedArgs{
		"service_id":        DOC_SERVICE_ID,
		"widget_id":         DOC_WIDGET_ID,
		"protection_period": int(DATA_PROTECTION_PERIOD.Seconds()),
	}
	_, err := db.Exec(ctx, query1, args1)
	if err != nil {
		return err
	}

	// set or update dvc_svc to active
	query2 := `INSERT INTO widgets (
                     id,
                     service_id,
                     template,
                     source
				) VALUES (
						@widget_id,
			            @service_id,
			            @template,
			            @source
				) ON CONFLICT (id) DO UPDATE
				    SET
				        template = EXCLUDED.template,
				        source = EXCLUDED.source,
						deleted_at = null`
	args2 := pgx.NamedArgs{
		"widget_id":  DOC_WIDGET_ID,
		"service_id": DOC_SERVICE_ID,
		"template":   "number",
		"source":     `{"type": "static", "data": 123}`,
	}
	_, err = db.Exec(ctx, query2, args2)
	return err
}
