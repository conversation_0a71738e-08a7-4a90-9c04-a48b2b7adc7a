package device_names

import (
	"api.appio.so/models"
	"api.appio.so/repositories"
	"context"
	"fmt"
	"io"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// DeviceParser defines the interface for parsing device data from different sources
type DeviceParser interface {
	// ParseData parses the downloaded data and returns device names
	ParseData(data []byte) ([]models.DeviceName, error)
}

// HTTPClient interface for testing
type HTTPClient interface {
	Get(url string) (*http.Response, error)
}

// DefaultHTTPClient wraps http.Client
type DefaultHTTPClient struct {
	client *http.Client
}

func NewDefaultHTTPClient() *DefaultHTTPClient {
	return &DefaultHTTPClient{
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (c *DefaultHTTPClient) Get(url string) (*http.Response, error) {
	return c.client.Get(url)
}

// DownloadData downloads data from the given URL
func DownloadData(ctx context.Context, client HTTPClient, url string, logger *zap.Logger) ([]byte, error) {
	logger.Info("downloading data", zap.String("url", url))
	
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("creating request: %w", err)
	}
	
	resp, err := client.Get(req.URL.String())
	if err != nil {
		return nil, fmt.Errorf("downloading data: %w", err)
	}
	defer resp.Body.Close()
	
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
	
	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("reading response body: %w", err)
	}
	
	logger.Info("data downloaded successfully", 
		zap.String("url", url),
		zap.Int("size_bytes", len(data)))
	
	return data, nil
}

// Parse handles the complete flow for a single parser
func Parse(ctx context.Context, parser DeviceParser, client HTTPClient, url string, repo repositories.DeviceNameRepositoryInterface, logger *zap.Logger) error {
	logger.Info("processing parser", zap.String("url", url))

	// Download data
	data, err := DownloadData(ctx, client, url, logger)
	if err != nil {
		return fmt.Errorf("downloading data: %w", err)
	}

	// Parse data
	deviceNames, err := parser.ParseData(data)
	if err != nil {
		return fmt.Errorf("parsing data: %w", err)
	}

	if len(deviceNames) == 0 {
		logger.Warn("no device names parsed", zap.String("url", url))
		return nil
	}

	// Store in database
	if err := repo.Import(ctx, deviceNames); err != nil {
		return fmt.Errorf("importing device names: %w", err)
	}

	logger.Info("successfully processed device names",
		zap.String("url", url),
		zap.Int("count", len(deviceNames)))

	return nil
}
