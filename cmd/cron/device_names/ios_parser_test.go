package device_names

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)



func TestIOSParser_ParseData(t *testing.T) {
	parser := NewIOSParser()

	t.Run("valid JSON with iPhone and iPad devices", func(t *testing.T) {
		jsonData := `[
			{
				"type": "iPhone",
				"name": "iPhone 13",
				"key": "iPhone14,5"
			},
			{
				"type": "iPhone",
				"name": "iPhone 13 Pro",
				"key": "iPhone14,2"
			},
			{
				"type": "iPad",
				"name": "iPad Pro",
				"key": "iPad13,1"
			}
		]`

		deviceNames, err := parser.ParseData([]byte(jsonData))
		require.NoError(t, err)
		assert.Len(t, deviceNames, 3) // iPhone and iPad devices should be included

		// Check first device (iPhone)
		assert.Equal(t, "apple", deviceNames[0].Brand)
		assert.Equal(t, "iPhone14,5", deviceNames[0].Model)
		assert.Equal(t, "iPhone 13", deviceNames[0].Name)

		// Check second device (iPhone)
		assert.Equal(t, "apple", deviceNames[1].Brand)
		assert.Equal(t, "iPhone14,2", deviceNames[1].Model)
		assert.Equal(t, "iPhone 13 Pro", deviceNames[1].Name)

		// Check third device (iPad)
		assert.Equal(t, "apple", deviceNames[2].Brand)
		assert.Equal(t, "iPad13,1", deviceNames[2].Model)
		assert.Equal(t, "iPad Pro", deviceNames[2].Name)
	})

	t.Run("empty JSON array", func(t *testing.T) {
		jsonData := `[]`

		deviceNames, err := parser.ParseData([]byte(jsonData))
		require.NoError(t, err)
		assert.Len(t, deviceNames, 0)
	})

	t.Run("invalid JSON", func(t *testing.T) {
		jsonData := `invalid json`

		deviceNames, err := parser.ParseData([]byte(jsonData))
		assert.Error(t, err)
		assert.Nil(t, deviceNames)
	})

	t.Run("devices with missing fields", func(t *testing.T) {
		jsonData := `[
			{
				"type": "iPhone",
				"name": "iPhone 13",
				"key": ""
			},
			{
				"type": "iPhone",
				"name": "",
				"key": "iPhone14,2"
			},
			{
				"type": "iPhone",
				"name": "iPhone 13 Pro",
				"key": "iPhone14,3"
			}
		]`

		deviceNames, err := parser.ParseData([]byte(jsonData))
		require.NoError(t, err)
		assert.Len(t, deviceNames, 1) // Only the complete device should be included

		assert.Equal(t, "apple", deviceNames[0].Brand)
		assert.Equal(t, "iPhone14,3", deviceNames[0].Model)
		assert.Equal(t, "iPhone 13 Pro", deviceNames[0].Name)
	})

	t.Run("case insensitive type filtering", func(t *testing.T) {
		jsonData := `[
			{
				"type": "IPHONE",
				"name": "iPhone 13",
				"key": "iPhone14,5"
			},
			{
				"type": "iphone",
				"name": "iPhone 13 Pro",
				"key": "iPhone14,2"
			}
		]`

		deviceNames, err := parser.ParseData([]byte(jsonData))
		require.NoError(t, err)
		assert.Len(t, deviceNames, 2)
	})
}
