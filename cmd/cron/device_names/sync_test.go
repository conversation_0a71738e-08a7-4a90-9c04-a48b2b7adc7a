package device_names

import (
	"api.appio.so/models"
	"context"
	"errors"
	"io"
	"net/http"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap/zaptest"
)

// MockHTTPClient for testing
type MockHTTPClient struct {
	mock.Mock
}

func (m *MockHTTPClient) Get(url string) (*http.Response, error) {
	args := m.Called(url)
	return args.Get(0).(*http.Response), args.Error(1)
}

// MockDeviceNameRepository for testing
type MockDeviceNameRepository struct {
	mock.Mock
}

func (m *MockDeviceNameRepository) FindMarketingName(ctx context.Context, brand, model string) (string, error) {
	args := m.Called(ctx, brand, model)
	return args.String(0), args.Error(1)
}

func (m *MockDeviceNameRepository) Import(ctx context.Context, deviceNames []models.DeviceName) error {
	args := m.Called(ctx, deviceNames)
	return args.Error(0)
}

func TestParse(t *testing.T) {
	logger := zaptest.NewLogger(t)

	t.Run("successful processing", func(t *testing.T) {
		// Setup mocks
		mockClient := &MockHTTPClient{}
		mockRepo := &MockDeviceNameRepository{}
		parser := NewIOSParser()

		// Mock HTTP response
		jsonData := `[{"type": "iPhone", "name": "iPhone 13", "key": "iPhone14,5"}]`
		resp := &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(strings.NewReader(jsonData)),
		}
		testURL := "https://test.example.com/devices.json"
		mockClient.On("Get", testURL).Return(resp, nil)

		// Mock repository call
		expectedDeviceNames := []models.DeviceName{
			{Brand: "apple", Model: "iPhone14,5", Name: "iPhone 13"},
		}
		mockRepo.On("Import", mock.Anything, expectedDeviceNames).Return(nil)

		// Execute
		err := Parse(context.Background(), parser, mockClient, testURL, mockRepo, logger)

		// Verify
		require.NoError(t, err)
		mockClient.AssertExpectations(t)
		mockRepo.AssertExpectations(t)
	})

	t.Run("download error", func(t *testing.T) {
		mockClient := &MockHTTPClient{}
		mockRepo := &MockDeviceNameRepository{}
		parser := NewIOSParser()

		// Mock HTTP error
		testURL := "https://test.example.com/devices.json"
		mockClient.On("Get", testURL).Return((*http.Response)(nil), errors.New("network error"))

		// Execute
		err := Parse(context.Background(), parser, mockClient, testURL, mockRepo, logger)

		// Verify
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "downloading data")
		mockClient.AssertExpectations(t)
		mockRepo.AssertNotCalled(t, "Import")
	})

	t.Run("repository error", func(t *testing.T) {
		mockClient := &MockHTTPClient{}
		mockRepo := &MockDeviceNameRepository{}
		parser := NewIOSParser()

		// Mock HTTP response
		jsonData := `[{"type": "iPhone", "name": "iPhone 13", "key": "iPhone14,5"}]`
		resp := &http.Response{
			StatusCode: 200,
			Body:       io.NopCloser(strings.NewReader(jsonData)),
		}
		testURL := "https://test.example.com/devices.json"
		mockClient.On("Get", testURL).Return(resp, nil)

		// Mock repository error
		mockRepo.On("Import", mock.Anything, mock.Anything).Return(errors.New("database error"))

		// Execute
		err := Parse(context.Background(), parser, mockClient, testURL, mockRepo, logger)

		// Verify
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "importing device names")
		mockClient.AssertExpectations(t)
		mockRepo.AssertExpectations(t)
	})
}


