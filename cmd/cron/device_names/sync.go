package device_names

import (
	"api.appio.so/cmd/cron/config"
	"api.appio.so/repositories"
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

const (
	importTimeout = 10 * time.Minute // Timeout for the entire import operation
)

// ImportDeviceNames downloads and imports device names from both iOS and Android sources
func ImportDeviceNames(db *pgxpool.Pool, iosCfg config.IOS, androidCfg config.Android, logger *zap.Logger) {
	if err := importDeviceNames(db, iosCfg, androidCfg, logger); err != nil {
		logger.Error("device names import error", zap.Error(err))
	}
}

func importDeviceNames(db *pgxpool.Pool, iosCfg config.IOS, androidCfg config.Android, logger *zap.Logger) error {
	ctx, cancel := context.WithTimeout(context.Background(), importTimeout)
	defer cancel()

	logger.Info("starting device names import")
	startTime := time.Now()

	// Initialize dependencies
	repo := repositories.NewDeviceNameRepository(db)
	httpClient := NewDefaultHTTPClient()

	// Create parsers with their URLs
	parsers := []struct {
		parser DeviceParser
		url    string
	}{
		{NewIOSParser(), iosCfg.DeviceNamesURL},
		{NewAndroidParser(), androidCfg.DeviceNamesURL},
	}

	var totalProcessed int
	var errors []error

	// Process each parser
	for _, p := range parsers {
		if err := Parse(ctx, p.parser, httpClient, p.url, repo, logger); err != nil {
			errors = append(errors, fmt.Errorf("import failed for %s: %w", p.url, err))
			logger.Error("parser failed",
				zap.String("url", p.url),
				zap.Error(err))
			continue
		}
		totalProcessed++
	}

	duration := time.Since(startTime)
	
	// Log final results
	if len(errors) > 0 {
		logger.Error("device names import completed with errors",
			zap.Int("successful_parsers", totalProcessed),
			zap.Int("failed_parsers", len(errors)),
			zap.Duration("duration", duration),
			zap.Errors("errors", errors))

		// Return error if all parsers failed
		if totalProcessed == 0 {
			return fmt.Errorf("all parsers failed: %v", errors)
		}
	} else {
		logger.Info("device names import completed successfully",
			zap.Int("parsers_processed", totalProcessed),
			zap.Duration("duration", duration))
	}

	return nil
}
