package device_names

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"strings"
	"unicode/utf16"
	"unicode/utf8"

	"api.appio.so/models"
)

// AndroidParser handles parsing Android device data from Google Play CSV
type AndroidParser struct{}

func NewAndroidParser() *AndroidParser {
	return &AndroidParser{}
}

func (p *AndroidParser) ParseData(data []byte) ([]models.DeviceName, error) {
	// Handle different encodings and BOMs
	csvText, err := p.decodeCSVData(data)
	if err != nil {
		return nil, fmt.Errorf("decoding CSV data: %w", err)
	}

	reader := csv.NewReader(strings.NewReader(csvText))
	reader.FieldsPerRecord = -1    // Allow variable field count
	reader.LazyQuotes = true       // Allow bare quotes in fields to handle malformed CSV
	reader.TrimLeadingSpace = true // Trim leading whitespace for cleaner data

	records, err := reader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("reading CSV: %w", err)
	}

	if len(records) == 0 {
		return nil, fmt.Errorf("CSV is empty")
	}

	// Find column indices from header
	header := records[0]
	indices, err := p.findColumnIndices(header)
	if err != nil {
		return nil, err
	}

	var deviceNames []models.DeviceName

	// Process data rows (skip header)
	for _, record := range records[1:] {
		deviceName, err := p.parseRecord(record, indices)
		if err != nil {
			// Log error but continue processing other records
			continue
		}

		if deviceName != nil {
			deviceNames = append(deviceNames, *deviceName)
		}

		//// Log progress for large datasets
		//if (i+1)%10000 == 0 {
		//	fmt.Printf("Processed %d Android device names records\n", i+1)
		//}
	}

	return deviceNames, nil
}

// decodeCSVData handles different encodings and BOMs
func (p *AndroidParser) decodeCSVData(data []byte) (string, error) {
	// Check for UTF-16 LE BOM
	if len(data) >= 2 && data[0] == 0xFF && data[1] == 0xFE {
		// UTF-16 LE encoding
		data = data[2:] // Remove BOM
		if len(data)%2 != 0 {
			return "", fmt.Errorf("invalid UTF-16 data: odd number of bytes")
		}

		// Convert UTF-16 LE to UTF-8
		u16s := make([]uint16, len(data)/2)
		for i := 0; i < len(u16s); i++ {
			u16s[i] = uint16(data[i*2]) | uint16(data[i*2+1])<<8
		}
		runes := utf16.Decode(u16s)
		return string(runes), nil
	}

	// Check for UTF-16 BE BOM
	if len(data) >= 2 && data[0] == 0xFE && data[1] == 0xFF {
		// UTF-16 BE encoding
		data = data[2:] // Remove BOM
		if len(data)%2 != 0 {
			return "", fmt.Errorf("invalid UTF-16 data: odd number of bytes")
		}

		// Convert UTF-16 BE to UTF-8
		u16s := make([]uint16, len(data)/2)
		for i := 0; i < len(u16s); i++ {
			u16s[i] = uint16(data[i*2])<<8 | uint16(data[i*2+1])
		}
		runes := utf16.Decode(u16s)
		return string(runes), nil
	}

	// Check for UTF-8 BOM
	data = bytes.TrimPrefix(data, []byte{0xEF, 0xBB, 0xBF})

	// Validate UTF-8 and return as string
	if !utf8.Valid(data) {
		return "", fmt.Errorf("invalid UTF-8 data")
	}

	return string(data), nil
}

// columnIndices holds the positions of required columns
type columnIndices struct {
	retailBranding int // maps to brand
	marketingName  int // maps to name
	model          int // maps to model
}

func (p *AndroidParser) findColumnIndices(header []string) (*columnIndices, error) {
	indices := &columnIndices{
		retailBranding: -1,
		marketingName:  -1,
		model:          -1,
	}

	for i, col := range header {
		colLower := strings.ToLower(strings.TrimSpace(col))
		switch colLower {
		case "retail branding":
			indices.retailBranding = i
		case "marketing name":
			indices.marketingName = i
		case "model":
			indices.model = i
		}
	}

	// Verify all required columns were found
	if indices.retailBranding == -1 {
		return nil, fmt.Errorf("missing required column: retail branding")
	}
	if indices.marketingName == -1 {
		return nil, fmt.Errorf("missing required column: marketing name")
	}
	if indices.model == -1 {
		return nil, fmt.Errorf("missing required column: model")
	}

	return indices, nil
}

func (p *AndroidParser) parseRecord(record []string, indices *columnIndices) (*models.DeviceName, error) {
	// Check if the record has enough fields
	maxIndex := maxIndex(maxIndex(indices.retailBranding, indices.marketingName), indices.model)
	if len(record) <= maxIndex {
		return nil, fmt.Errorf("record has insufficient fields")
	}

	retailBranding := strings.TrimSpace(record[indices.retailBranding])
	marketingName := strings.TrimSpace(record[indices.marketingName])
	model := strings.TrimSpace(record[indices.model])

	// Skip records with empty required fields
	if retailBranding == "" || marketingName == "" || model == "" {
		return nil, nil
	}

	deviceName := &models.DeviceName{
		Brand: retailBranding, // retail branding becomes our brand
		Model: model,          // model as-is, no combination here
		Name:  marketingName,  // marketing name becomes our display name
	}

	return deviceName, nil
}

func maxIndex(a, b int) int {
	if a > b {
		return a
	}
	return b
}
