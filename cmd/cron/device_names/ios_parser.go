package device_names

import (
	"encoding/json"
	"fmt"
	"strings"

	"api.appio.so/models"
)

const (
	IOSBrand = "apple"
)

// IOSParser handles parsing iOS device data from Apple's API
type IOSParser struct{}

// IOSDevice represents a device from the Apple API
type IOSDevice struct {
	Type string `json:"type"`
	Name string `json:"name"`
	Key  string `json:"key"`
}

func NewIOSParser() *IOSParser {
	return &IOSParser{}
}

func (p *IOSParser) ParseData(data []byte) ([]models.DeviceName, error) {
	var devices []IOSDevice
	if err := json.Unmarshal(data, &devices); err != nil {
		return nil, fmt.Errorf("unmarshaling JSON: %w", err)
	}

	var deviceNames []models.DeviceName

	for _, device := range devices {
		// Only process `iPhone`, `iPad`, `iPad *` devices as specified
		t := strings.ToLower(device.Type)
		if t != "iphone" && !strings.HasPrefix(t, "ipad") {
			continue
		}

		// Skip devices with empty required fields
		if device.Name == "" || device.Key == "" {
			continue
		}

		deviceName := models.DeviceName{
			Brand: IOSBrand,    // Always "apple" for iOS
			Model: device.Key,  // The device identifier (e.g., "iPhone14,5")
			Name:  device.Name, // The marketing name (e.g., "iPhone 13")
		}

		deviceNames = append(deviceNames, deviceName)
	}

	return deviceNames, nil
}
