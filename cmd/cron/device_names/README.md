# Device Names Import Cron Job

This package implements a daily cron job that downloads and imports device marketing names from iOS and Android sources into the `device_names` database table.

## Overview

The system consists of:

1. **Generic Parser Interface** (`parser.go`) - Common functionality for downloading and processing device data
2. **iOS Parser** (`ios_parser.go`) - Handles Apple device data from their API
3. **Android Parser** (`android_parser.go`) - <PERSON>les Google Play CSV device data
4. **Import Orchestrator** (`sync.go`) - Coordinates the entire import process
5. **Repository Extensions** - Import functionality for efficient database operations

## Data Sources

### iOS Devices
- **Source**: Configured via `ios.device_names_url` in cron config
- **Format**: JSON array of device objects
- **Filtering**: Only devices with `type: "iPhone"` are processed
- **Mapping**:
  - `brand` → "apple" (hardcoded)
  - `model` → device `key` field (e.g., "iPhone14,5")
  - `name` → device `name` field (e.g., "iPhone 13")

### Android Devices
- **Source**: Configured via `android.device_names_url` in cron config
- **Format**: CSV with headers
- **Required Columns**: "Retail Branding", "Marketing Name", "Model"
- **Mapping**:
  - `brand` → "Retail Branding" column
  - `model` → "Model" column
  - `name` → "Marketing Name" column

## Database Schema

The data is stored in the `device_names` table. See `configs/db/db-init/appio.sql` for the complete schema definition.

## Scheduling

The cron job runs daily and is configured in `cmd/cron/main.go`.
