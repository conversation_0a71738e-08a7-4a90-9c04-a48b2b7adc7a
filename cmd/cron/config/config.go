package config

import (
	"api.appio.so/pkg/config"
	"fmt"
	"github.com/spf13/viper"
)

type Config struct {
	FileName string
	DB       config.DBConfig
	Server   config.ServerConfig
	IOS      IOS
	Android  Android
	Sentry   config.SentryConfig
}

type IOS struct {
	KeyFile         string `mapstructure:"key_file"`
	KeyId           string `mapstructure:"key_id"`
	TeamId          string `mapstructure:"team_id"`
	Topic           string `mapstructure:"topic"`
	ServerName      string `mapstructure:"server_name"`
	DefaultCategory string `mapstructure:"default_category"`
	DeviceNamesURL  string `mapstructure:"device_names_url"`
}

type Android struct {
	KeyFile        string `mapstructure:"key_file"`
	BaseURL        string `mapstructure:"base_url"`
	DeviceNamesURL string `mapstructure:"device_names_url"`
}

func LoadConfig(fileName string) (*Config, error) {
	viper.SetConfigName(fileName)
	viper.SetConfigType("toml")
	viper.AddConfigPath("./configs")

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("reading config file: %w", err)
	}

	var c Config
	if err := viper.Unmarshal(&c); err != nil {
		return nil, fmt.Errorf("unmarshaling config: %w", err)
	}
	c.FileName = fileName

	viper.AutomaticEnv() // Automatically read environment variables. f.e. SERVER_POR="123"
	viper.WatchConfig()  // listen to config changes

	return &c, nil
}
