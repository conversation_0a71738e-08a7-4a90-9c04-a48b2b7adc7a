package main

import (
	"context"
	"flag"
	"log"
	"time"

	"github.com/getsentry/sentry-go"
	"api.appio.so/cmd/cron/docs"
	"api.appio.so/cmd/cron/config"
	"api.appio.so/pkg/database"
	"api.appio.so/pkg"
	"github.com/appio-so/go-zaplog"
	"go.uber.org/zap"
)

// Manual reset of docs data
func main() {
	// Parse command line flags
	debug := flag.Bool("debug", false, "Enable debug logging")
	flag.Parse()

	// Load config
	cfg, err := config.LoadConfig("config.cron")
	if err != nil {
		log.Fatalf("failed to load config: %v", err)
	}

	// Determine log level
	logLevel := cfg.Server.LogLevel
	if *debug {
		logLevel = "debug"
	}

	// Initialize logger
	logger, err := zaplog.NewLogger(logLevel, cfg.Server.Env, cfg.Server.Name)
	if err != nil {
		log.Fatalf("failed to initialize logger: %v", err)
	}
	defer logger.Sync()

	logger = pkg.NewSentryLogger(logger, &cfg.Server, &cfg.Sentry)
	defer sentry.Flush(2 * time.Second)

	// Initialize database
	db, _, err := database.InitDatabase(context.Background(), &cfg.DB, logger)
	if err != nil {
		logger.Fatal("failed to init database", zap.Error(err))
	}
	defer db.Close()

	logger.Info("starting manual docs reset")

	// Run the reset function directly
	docs.Reset(db, logger)

	logger.Info("manual docs reset completed")
}
