package notification_status

import (
	"api.appio.so/models/queue"
)

type Status queue.Status

const (
	Created   Status = Status(queue.Created)
	Queued    Status = Status(queue.Queued)
	Completed Status = Status(queue.Completed)
	Failed    Status = Status(queue.Failed)
	Skipped   Status = Status(queue.Skipped) // f.e. when service is deactivated
)

func All() []Status {
	return []Status{
		Created,
		Queued,
		Completed,
		Failed,
		Skipped,
	}
}

func (s Status) IsValidStatus() bool {
	for _, validStatus := range All() {
		if s == validStatus {
			return true
		}
	}
	return false
}

func (s Status) String() string {
	return string(s)
}
