package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestServiceRequest_Validate_ColorFields(t *testing.T) {
	t.Run("Valid color formats", func(t *testing.T) {
		req := ServiceRequest{
			Title:           "Test Service",
			LogoURL:         "https://example.com/logo.png",
			TextColor:       "#FF0000",
			BackgroundColor: "#00FF00",
			AccentColor:     "#0000FF",
		}

		err := req.Validate(false)
		assert.Nil(t, err)
	})

	t.Run("Valid color formats - mixed case", func(t *testing.T) {
		req := ServiceRequest{
			Title:           "Test Service",
			LogoURL:         "https://example.com/logo.png",
			TextColor:       "#fF0000",
			BackgroundColor: "#00fF00",
			AccentColor:     "#0000fF",
		}

		err := req.Validate(false)
		assert.Nil(t, err)
	})

	t.Run("Valid color formats - lowercase", func(t *testing.T) {
		req := ServiceRequest{
			Title:           "Test Service",
			LogoURL:         "https://example.com/logo.png",
			TextColor:       "#ff0000",
			BackgroundColor: "#00ff00",
			AccentColor:     "#0000ff",
		}

		err := req.Validate(false)
		assert.Nil(t, err)
	})

	t.Run("Empty color fields are valid", func(t *testing.T) {
		req := ServiceRequest{
			Title:           "Test Service",
			LogoURL:         "https://example.com/logo.png",
			TextColor:       "",
			BackgroundColor: "",
			AccentColor:     "",
		}

		err := req.Validate(false)
		assert.Nil(t, err)
	})

	t.Run("Invalid text color format", func(t *testing.T) {
		req := ServiceRequest{
			Title:     "Test Service",
			LogoURL:   "https://example.com/logo.png",
			TextColor: "FF0000", // Missing #
		}

		err := req.Validate(false)
		assert.NotNil(t, err)
		assert.Len(t, err.Fields, 1)
		assert.Equal(t, "text_color", err.Fields[0].Field)
		assert.Contains(t, err.Fields[0].Reason, "#RRGGBB")
	})

	t.Run("Invalid background color format", func(t *testing.T) {
		req := ServiceRequest{
			Title:           "Test Service",
			LogoURL:         "https://example.com/logo.png",
			BackgroundColor: "#00FF", // Too short
		}

		err := req.Validate(false)
		assert.NotNil(t, err)
		assert.Len(t, err.Fields, 1)
		assert.Equal(t, "background_color", err.Fields[0].Field)
		assert.Contains(t, err.Fields[0].Reason, "#RRGGBB")
	})

	t.Run("Invalid accent color format", func(t *testing.T) {
		req := ServiceRequest{
			Title:       "Test Service",
			LogoURL:     "https://example.com/logo.png",
			AccentColor: "#00FF", // Too short
		}

		err := req.Validate(false)
		assert.NotNil(t, err)
		assert.Len(t, err.Fields, 1)
		assert.Equal(t, "accent_color", err.Fields[0].Field)
		assert.Contains(t, err.Fields[0].Reason, "#RRGGBB")
	})

	t.Run("Invalid color format - too long", func(t *testing.T) {
		req := ServiceRequest{
			Title:     "Test Service",
			LogoURL:   "https://example.com/logo.png",
			TextColor: "#FF00000", // Too long
		}

		err := req.Validate(false)
		assert.NotNil(t, err)
		assert.Len(t, err.Fields, 1)
		assert.Equal(t, "text_color", err.Fields[0].Field)
		assert.Contains(t, err.Fields[0].Reason, "#RRGGBB")
	})

	t.Run("Invalid color format - invalid characters", func(t *testing.T) {
		req := ServiceRequest{
			Title:     "Test Service",
			LogoURL:   "https://example.com/logo.png",
			TextColor: "#GG0000", // Invalid hex characters
		}

		err := req.Validate(false)
		assert.NotNil(t, err)
		assert.Len(t, err.Fields, 1)
		assert.Equal(t, "text_color", err.Fields[0].Field)
		assert.Contains(t, err.Fields[0].Reason, "#RRGGBB")
	})

	t.Run("Multiple invalid color formats", func(t *testing.T) {
		req := ServiceRequest{
			Title:           "Test Service",
			LogoURL:         "https://example.com/logo.png",
			TextColor:       "red",     // Invalid format
			BackgroundColor: "#ZZZZZZ", // Invalid hex characters
			AccentColor:     "#red",    // Invalid format
		}

		err := req.Validate(false)
		assert.NotNil(t, err)
		assert.Len(t, err.Fields, 3)

		// Check that both fields have errors
		fieldNames := make([]string, len(err.Fields))
		for i, field := range err.Fields {
			fieldNames[i] = field.Field
		}
		assert.Contains(t, fieldNames, "text_color")
		assert.Contains(t, fieldNames, "background_color")
		assert.Contains(t, fieldNames, "accent_color")
	})
}
