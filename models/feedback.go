package models

import (
	"api.appio.so/pkg"
)

const FeedbackPrefix = "fdb"

type FeedbackRequest struct {
	Message string `json:"message"`
}

func (f FeedbackRequest) Validate() *pkg.ValidationError {
	var errFields []pkg.ValidationField

	if f.Message == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "message",
			Reason: "message is required",
		})
	}
	if len(f.Message) > 5000 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "message",
			Reason: "message is too long (max 5000 characters)",
		})
	}

	if len(errFields) > 0 {
		return &pkg.ValidationError{
			Entity: "feedback",
			DocUrl: "https://docs.appio.so/",
			Fields: errFields,
		}
	}
	return nil
}
