package models

import (
	"encoding/json"
	"fmt"
	"time"

	"api.appio.so/models/notification_status"
	"api.appio.so/models/notification_type"
	"api.appio.so/pkg"

	"github.com/appio-so/go-appioid"
)

const NotificationPrefix = "ntf"
const ScheduledAtDaysLimit = 30

type Notification struct {
	ID          *appioid.ID
	ServiceID   *appioid.ID
	CreatedAt   time.Time
	ScheduledAt time.Time
	Type        notification_type.Type
	Status      notification_status.Status
	Payload     json.RawMessage
}

type NotificationRequest struct {
	Payload     json.RawMessage `json:"payload"`
	ScheduledAt *time.Time      `json:"scheduled_at"`
}

// User facing error
func (n NotificationRequest) Validate() *pkg.ValidationError {
	var errFields []pkg.ValidationField

	// TODO: should payload have more structure? it does in NotificationPayload. Payload is different for iOS and Android

	if n.Payload == nil {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "payload",
			Reason: "payload is required",
		})
	}
	if n.ScheduledAt != nil && n.ScheduledAt.Before(time.Now()) {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "scheduled_at",
			Reason: "scheduled_at is in the past",
		})
	}
	if n.ScheduledAt != nil && n.ScheduledAt.After(time.Now().Add(ScheduledAtDaysLimit*24*time.Hour)) {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "scheduled_at",
			Reason: fmt.Sprintf("scheduled_at is more than %d days in the future", ScheduledAtDaysLimit),
		})
	}

	if len(errFields) > 0 {
		return &pkg.ValidationError{
			Entity: "notification",
			DocUrl: "https://docs.appio.so/#api-notifications",
			Fields: errFields,
		}
	}
	return nil
}

type NotificationResponse struct {
	ID          *appioid.ID                `json:"id"`
	ServiceID   *appioid.ID                `json:"service_id"`
	Status      notification_status.Status `json:"status"`
	Payload     json.RawMessage            `json:"payload"`
	ScheduledAt *time.Time                 `json:"scheduled_at"`
}

type NotificationResponseWithStats struct {
	ID            *appioid.ID                `json:"id"`
	ServiceID     *appioid.ID                `json:"service_id"`
	Status        notification_status.Status `json:"status"`
	Payload       json.RawMessage            `json:"payload"`
	ScheduledAt   *time.Time                 `json:"scheduled_at"`
	DeliveryStats NotificationDeliveryStats  `json:"delivery_stats"`
}

type NotificationDeliveryStats struct {
	Total     int `json:"total"`
	Created   int `json:"created"`
	Queued    int `json:"queued"`
	Completed int `json:"completed"`
	Failed    int `json:"failed"`
}
