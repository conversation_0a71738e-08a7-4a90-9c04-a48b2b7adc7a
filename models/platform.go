package models

import "fmt"

type Platform string

// Does not validate during unmarshalling
const (
	PlatformAndroid Platform = "android"
	PlatformIOS     Platform = "ios"
)

// User facing error
func (p Platform) Validate() error {
	switch p {
	case PlatformAndroid, PlatformIOS:
		return nil
	default:
		return fmt.Errorf("invalid platform: %s", p)
	}
}

func ParsePlatform(s string) (Platform, error) {
	platform := Platform(s)
	if err := platform.Validate(); err != nil {
		return "", err
	}
	return platform, nil
}
