package models

import (
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFeedbackRequest_Validate(t *testing.T) {
	t.Run("Valid feedback request", func(t *testing.T) {
		req := FeedbackRequest{
			Message: "This is a valid feedback message",
		}

		err := req.Validate()
		assert.Nil(t, err)
	})

	t.Run("Empty message", func(t *testing.T) {
		req := FeedbackRequest{
			Message: "",
		}

		err := req.Validate()
		assert.NotNil(t, err)
		assert.Equal(t, "feedback", err.Entity)
		assert.Len(t, err.Fields, 1)
		assert.Equal(t, "message", err.<PERSON>[0].Field)
		assert.Equal(t, "message is required", err.<PERSON>[0].Reason)
	})

	t.Run("Message too long", func(t *testing.T) {
		// Create a message longer than 5000 characters
		longMessage := strings.Repeat("a", 5001)
		req := FeedbackRequest{
			Message: longMessage,
		}

		err := req.<PERSON>idate()
		assert.NotNil(t, err)
		assert.Equal(t, "feedback", err.<PERSON>tity)
		assert.Len(t, err.Fields, 1)
		assert.Equal(t, "message", err.<PERSON>[0].Field)
		assert.Equal(t, "message is too long (max 5000 characters)", err.Fields[0].Reason)
	})

	t.Run("Message at max length", func(t *testing.T) {
		// Create a message exactly 5000 characters
		maxMessage := strings.Repeat("a", 5000)
		req := FeedbackRequest{
			Message: maxMessage,
		}

		err := req.Validate()
		assert.Nil(t, err)
	})
}
