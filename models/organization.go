package models

import (
	"api.appio.so/pkg"
	"time"

	"github.com/appio-so/go-appioid"
)

const OrganizationPrefix = "org"

type Organization struct {
	ID            *appioid.ID `json:"id"`
	CreatedAt     time.Time   `json:"-"` // don't output in API
	DeactivatedAt *time.Time  `json:"-"` // don't output in API, supports NULL values
	OrganizationRequest
}

type OrganizationRequest struct {
	Name string `json:"name"`
}

func (o OrganizationRequest) Validate(isUpdate bool) *pkg.ValidationError {
	var errFields []pkg.ValidationField

	if !isUpdate && o.Name == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "name",
			Reason: "name is required",
		})
	}
	if len(o.Name) > 255 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "name",
			Reason: "name is too long (max 255 characters)",
		})
	}

	if len(errFields) > 0 {
		return &pkg.ValidationError{
			Entity: "organization",
			DocUrl: "https://docs.appio.so/#api-organizations",
			Fields: errFields,
		}
	}
	return nil
}
