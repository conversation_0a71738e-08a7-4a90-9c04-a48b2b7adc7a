package models

import (
	"github.com/appio-so/go-appioid"
	"time"
)

const DeviceServicePrefix = "dvcsvc"

type DeviceService struct {
	ID                   *appioid.ID `json:"id"`
	DeviceID             *appioid.ID `json:"device_id"`
	ServiceID            *appioid.ID `json:"service_id"`
	CustomerUserID       string      `json:"customer_user_id"`
	CreatedAt            time.Time   `json:"-"` // don't output in API
	DeactivatedAt        *time.Time  `json:"-"` // don't output in API
	NotificationsEnabled bool        `json:"notifications_enabled"`
}
