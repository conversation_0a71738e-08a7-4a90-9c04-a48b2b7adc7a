package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestParsePlatform(t *testing.T) {
	t.Run("Valid platforms", func(t *testing.T) {
		tests := []struct {
			name     string
			input    string
			expected Platform
		}{
			{
				name:     "iOS platform",
				input:    "ios",
				expected: PlatformIOS,
			},
			{
				name:     "Android platform",
				input:    "android",
				expected: PlatformAndroid,
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result, err := ParsePlatform(tt.input)
				assert.NoError(t, err)
				assert.Equal(t, tt.expected, result)
			})
		}
	})

	t.Run("Invalid platforms", func(t *testing.T) {
		tests := []struct {
			name  string
			input string
		}{
			{
				name:  "Empty string",
				input: "",
			},
			{
				name:  "Invalid platform",
				input: "windows",
			},
			{
				name:  "Case sensitive - uppercase iOS",
				input: "iOS",
			},
			{
				name:  "Case sensitive - uppercase Android",
				input: "Android",
			},
			{
				name:  "Mixed case",
				input: "IOS",
			},
			{
				name:  "Special characters",
				input: "ios!",
			},
			{
				name:  "Numeric",
				input: "123",
			},
			{
				name:  "Whitespace",
				input: " ios ",
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				result, err := ParsePlatform(tt.input)
				assert.Error(t, err)
				assert.Equal(t, Platform(""), result)
				assert.Contains(t, err.Error(), "invalid platform")
			})
		}
	})
}

func TestPlatform_Validate(t *testing.T) {
	t.Run("Valid platforms", func(t *testing.T) {
		tests := []Platform{
			PlatformIOS,
			PlatformAndroid,
		}

		for _, platform := range tests {
			t.Run(string(platform), func(t *testing.T) {
				err := platform.Validate()
				assert.NoError(t, err)
			})
		}
	})

	t.Run("Invalid platforms", func(t *testing.T) {
		tests := []struct {
			name     string
			platform Platform
		}{
			{
				name:     "Empty platform",
				platform: Platform(""),
			},
			{
				name:     "Invalid platform",
				platform: Platform("windows"),
			},
			{
				name:     "Case sensitive",
				platform: Platform("iOS"),
			},
			{
				name:     "Special characters",
				platform: Platform("ios!"),
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				err := tt.platform.Validate()
				assert.Error(t, err)
				assert.Contains(t, err.Error(), "invalid platform")
			})
		}
	})
}
