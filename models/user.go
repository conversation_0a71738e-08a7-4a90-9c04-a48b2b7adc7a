package models

import (
	"api.appio.so/pkg"
	"net/mail"
	"time"

	"github.com/appio-so/go-appioid"
)

const UserPrefix = "usr"

type User struct {
	ID             *appioid.ID `json:"id"`
	OrganizationID *appioid.ID `json:"organization_id"`
	CreatedAt      time.Time   `json:"-"` // don't output in API
	DeactivatedAt  *time.Time  `json:"-"` // don't output in API, supports NULL values
	ExternalID     string      `json:"-"` // don't output in API
	PasswordHash   string      `json:"-"` // don't output in API
	UserData
}

type UserCreateRequest struct {
	OrganizationID *appioid.ID `json:"organization_id"`
	ExternalID     string      `json:"-"` // don't output in API
	UserData
}

type UserUpdateRequest struct {
	UserData
}

type UserData struct {
	Email string `json:"email"`
	Name  string `json:"name"`
}

func (u UserData) Validate(isUpdate bool) *pkg.ValidationError {
	var errFields []pkg.ValidationField

	// Email validation
	if !isUpdate && u.Email == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "email",
			Reason: "email is required",
		})
	}
	if len(u.Email) > 255 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "email",
			Reason: "email is too long (max 255 characters)",
		})
	}
	if u.Email != "" {
		_, err := mail.ParseAddress(u.Email)
		if err != nil {
			errFields = append(errFields, pkg.ValidationField{
				Field:  "email",
				Reason: "email must be a valid email address",
			})
		}
	}

	// Name validation
	if !isUpdate && u.Name == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "name",
			Reason: "name is required",
		})
	}
	if len(u.Name) > 255 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "name",
			Reason: "name is too long (max 255 characters)",
		})
	}

	if len(errFields) > 0 {
		return &pkg.ValidationError{
			Entity: "user",
			DocUrl: "https://docs.appio.so/#api-users",
			Fields: errFields,
		}
	}
	return nil
}

func (u UserCreateRequest) Validate() *pkg.ValidationError {
	// Validate organization ID
	if u.OrganizationID == nil {
		return &pkg.ValidationError{
			Entity: "user",
			DocUrl: "https://docs.appio.so/#api-users",
			Fields: []pkg.ValidationField{
				{
					Field:  "organization_id",
					Reason: "organization_id is required",
				},
			},
		}
	}

	// Validate the embedded UserRequest
	return u.UserData.Validate(false)
}

func (u UserUpdateRequest) Validate() *pkg.ValidationError {
	return u.UserData.Validate(true)
}
