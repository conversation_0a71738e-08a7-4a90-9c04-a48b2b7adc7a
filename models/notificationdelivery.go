package models

import (
	"api.appio.so/models/notification_status"
	"github.com/appio-so/go-appioid"
	"time"
)

const NotificationDeliveryPrefix = "ntfdlv"

type NotificationDelivery struct {
	ID             *appioid.ID                `json:"id"`
	NotificationID *appioid.ID                `json:"notification_id"`
	DeviceID       *appioid.ID                `json:"device_id"`
	CreatedAt      time.Time                  `json:"created_at"`
	UpdatedAt      *time.Time                 `json:"updated_at"`
	Status         notification_status.Status `json:"status"`
}
