package models

import (
	"regexp"
	"time"

	"api.appio.so/pkg"

	"github.com/appio-so/go-appioid"
)

const ServicePrefix = "svc"

type Service struct {
	ID             *appioid.ID `json:"id"`
	OrganizationID *appioid.ID `json:"-"` // don't output in API
	CreatedAt      time.Time   `json:"-"` // don't output in API
	DeactivatedAt  *time.Time  `json:"-"` // don't output in API, supports NULL values
	ServiceRequest
}

type ServiceWithWidgetConfigs struct {
	*Service
	WidgetConfigs []WidgetConfig `json:"widgets"`
}

type ServiceWithWidgets struct {
	*Service
	Widgets []Widget `json:"widgets"`
}

type ServiceRequest struct {
	Title           string `json:"title"`
	Description     string `json:"description"`
	LogoURL         string `json:"logo_url"`
	BannerURL       string `json:"banner_url"`
	URL             string `json:"url"`
	TextColor       string `json:"text_color"`
	BackgroundColor string `json:"background_color"`
	AccentColor     string `json:"accent_color"`
}

func (s ServiceRequest) Validate(isUpdate bool) *pkg.ValidationError {
	var errFields []pkg.ValidationField

	if !isUpdate && s.Title == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "title",
			Reason: "title is required",
		})
	}
	if len(s.Title) > 255 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "title",
			Reason: "title is too long (max 255 characters)",
		})
	}
	if len(s.Description) > 1000 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "description",
			Reason: "description is too long (max 10000 characters)",
		})
	}
	if !isUpdate && s.LogoURL == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "logo_url",
			Reason: "logo_url is required",
		})
	}
	if !isUpdate && len(s.LogoURL) > 512 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "logo_url",
			Reason: "logo_url is too long (max 512 characters)",
		})
	}
	if len(s.BannerURL) > 512 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "banner_url",
			Reason: "banner_url is too long (max 512 characters)",
		})
	}
	if len(s.URL) > 512 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "url",
			Reason: "url is too long (max 512 characters)",
		})
	}

	// Color validation: format #RRGGBB (any case, lower or upper or mixed)
	colorRegex := regexp.MustCompile(`^#[0-9A-Fa-f]{6}$`)
	if s.TextColor != "" && !colorRegex.MatchString(s.TextColor) {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "text_color",
			Reason: "text_color must be in format #RRGGBB (e.g., #FF0000, #00ff00)",
		})
	}
	if s.BackgroundColor != "" && !colorRegex.MatchString(s.BackgroundColor) {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "background_color",
			Reason: "background_color must be in format #RRGGBB (e.g., #FF0000, #00ff00)",
		})
	}
	if s.AccentColor != "" && !colorRegex.MatchString(s.AccentColor) {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "accent_color",
			Reason: "accent_color must be in format #RRGGBB (e.g., #FF0000, #00ff00)",
		})
	}

	if len(errFields) > 0 {
		return &pkg.ValidationError{
			Entity: "service",
			DocUrl: "https://docs.appio.so/#api-services",
			Fields: errFields,
		}
	}
	return nil
}
