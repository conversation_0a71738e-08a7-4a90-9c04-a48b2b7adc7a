package models

import (
	"github.com/stretchr/testify/assert"
	"strings"
	"testing"
)

func TestDeviceCreateRequest_Validate(t *testing.T) {
	t.Run("Valid device create request", func(t *testing.T) {
		req := DeviceCreateRequest{
			CustomerUserID: "user123",
			DeviceData: DeviceData{
				Name:                 "My iPhone",
				Platform:             PlatformIOS,
				OsVersion:            "17.0",
				Model:                "iPhone15,2",
				DeviceToken:          "abc123",
				NotificationsEnabled: true,
				DeviceIdentifier:     "device123",
			},
		}

		err := req.Validate()
		assert.Nil(t, err)
	})

	t.Run("Missing customer_user_id", func(t *testing.T) {
		req := DeviceCreateRequest{
			CustomerUserID: "",
			DeviceData: DeviceData{
				Name:                 "My iPhone",
				Platform:             PlatformIOS,
				OsVersion:            "17.0",
				Model:                "iPhone15,2",
				DeviceToken:          "abc123",
				NotificationsEnabled: true,
				DeviceIdentifier:     "device123",
			},
		}

		err := req.Validate()
		assert.NotNil(t, err)
		assert.Equal(t, "device", err.Entity)
		assert.Equal(t, "https://docs.appio.so/#api-devices", err.DocUrl)
		assert.Len(t, err.Fields, 1)
		assert.Equal(t, "customer_user_id", err.Fields[0].Field)
		assert.Equal(t, "customer_user_id is required", err.Fields[0].Reason)
	})

	t.Run("Invalid device data", func(t *testing.T) {
		req := DeviceCreateRequest{
			CustomerUserID: "user123",
			DeviceData: DeviceData{
				Name:                 "", // Missing required field
				Platform:             PlatformIOS,
				OsVersion:            "", // Missing required field
				Model:                "", // Missing required field
				DeviceToken:          "abc123",
				NotificationsEnabled: true,
				DeviceIdentifier:     "", // Missing required field
			},
		}

		err := req.Validate()
		assert.NotNil(t, err)
		assert.Equal(t, "device", err.Entity)
		assert.True(t, len(err.Fields) >= 4) // At least 4 validation errors
	})

	t.Run("Multiple validation errors", func(t *testing.T) {
		req := DeviceCreateRequest{
			CustomerUserID: "", // Missing
			DeviceData: DeviceData{
				Name:                 strings.Repeat("a", 256),   // Too long
				Platform:             Platform("invalid"), // Invalid platform
				OsVersion:            "",                         // Missing
				Model:                "",                         // Missing
				DeviceToken:          strings.Repeat("b", 256),   // Too long
				NotificationsEnabled: true,
				DeviceIdentifier:     "", // Missing
			},
		}

		err := req.Validate()
		assert.NotNil(t, err)
		assert.True(t, len(err.Fields) >= 5) // Multiple validation errors

		// Check that customer_user_id error is included
		hasCustomerUserIDError := false
		for _, field := range err.Fields {
			if field.Field == "customer_user_id" {
				hasCustomerUserIDError = true
				break
			}
		}
		assert.True(t, hasCustomerUserIDError)
	})
}

func TestDeviceUpdateRequest_Validate(t *testing.T) {
	t.Run("Valid device update request", func(t *testing.T) {
		req := DeviceUpdateRequest{
			Name:                 "Updated iPhone",
			OsVersion:            "17.1",
			Model:                "iPhone15,2",
			DeviceToken:          "updated_token",
			NotificationsEnabled: false,
			DeviceIdentifier:     "device123",
		}

		err := req.Validate()
		assert.Nil(t, err)
	})

	t.Run("Valid update with empty optional fields", func(t *testing.T) {
		req := DeviceUpdateRequest{
			Name:                 "", // Optional in updates
			OsVersion:            "", // Optional in updates
			Model:                "", // Optional in updates
			DeviceToken:          "",
			NotificationsEnabled: true,
			DeviceIdentifier:     "", // Optional in updates
		}

		err := req.Validate()
		assert.Nil(t, err)
	})

	t.Run("Invalid field lengths in update", func(t *testing.T) {
		req := DeviceUpdateRequest{
			Name:                 strings.Repeat("a", 256), // Too long
			OsVersion:            strings.Repeat("b", 51),  // Too long
			Model:                strings.Repeat("c", 101), // Too long
			DeviceToken:          strings.Repeat("d", 256), // Too long
			NotificationsEnabled: true,
			DeviceIdentifier:     strings.Repeat("e", 51), // Too long
		}

		err := req.Validate()
		assert.NotNil(t, err)
		assert.Equal(t, "device", err.Entity)
		assert.True(t, len(err.Fields) >= 5) // Multiple length validation errors
	})
}

func TestDeviceData_Validate(t *testing.T) {
	t.Run("Valid device data for create", func(t *testing.T) {
		data := DeviceData{
			Name:                 "My Device",
			Platform:             PlatformAndroid,
			OsVersion:            "14.0",
			Model:                "Pixel 8",
			DeviceToken:          "token123",
			NotificationsEnabled: true,
			DeviceIdentifier:     "device456",
		}

		errors := data.Validate(false) // isUpdate = false
		assert.Empty(t, errors)
	})

	t.Run("Valid device data for update", func(t *testing.T) {
		data := DeviceData{
			Name:                 "",                  // Optional in updates
			Platform:             Platform(""), // Optional in updates
			OsVersion:            "",                  // Optional in updates
			Model:                "",                  // Optional in updates
			DeviceToken:          "",
			NotificationsEnabled: false,
			DeviceIdentifier:     "", // Optional in updates
		}

		errors := data.Validate(true) // isUpdate = true
		assert.Empty(t, errors)
	})

	t.Run("Missing required fields for create", func(t *testing.T) {
		data := DeviceData{
			Name:                 "",                  // Required for create
			Platform:             Platform(""), // Required for create
			OsVersion:            "",                  // Required for create
			Model:                "",                  // Required for create
			DeviceToken:          "",
			NotificationsEnabled: false,
			DeviceIdentifier:     "", // Required for create
		}

		errors := data.Validate(false) // isUpdate = false
		assert.NotEmpty(t, errors)
		assert.True(t, len(errors) >= 4) // At least 4 required field errors

		// Check specific required field errors
		fieldErrors := make(map[string]string)
		for _, err := range errors {
			fieldErrors[err.Field] = err.Reason
		}

		assert.Contains(t, fieldErrors, "name")
		assert.Contains(t, fieldErrors, "platform")
		assert.Contains(t, fieldErrors, "os_version")
		assert.Contains(t, fieldErrors, "model")
		assert.Contains(t, fieldErrors, "device_identifier")
	})

	t.Run("Field length validations", func(t *testing.T) {
		data := DeviceData{
			Name:                 strings.Repeat("a", 256), // Too long (max 255)
			Platform:             PlatformIOS,
			OsVersion:            strings.Repeat("b", 51),  // Too long (max 50)
			Model:                strings.Repeat("c", 101), // Too long (max 100)
			DeviceToken:          strings.Repeat("d", 256), // Too long (max 255)
			NotificationsEnabled: true,
			DeviceIdentifier:     strings.Repeat("e", 51), // Too long (max 50)
		}

		errors := data.Validate(false)
		assert.NotEmpty(t, errors)
		assert.Len(t, errors, 5) // 5 length validation errors

		// Check specific length errors
		fieldErrors := make(map[string]string)
		for _, err := range errors {
			fieldErrors[err.Field] = err.Reason
		}

		assert.Contains(t, fieldErrors["name"], "too long")
		assert.Contains(t, fieldErrors["os_version"], "too long")
		assert.Contains(t, fieldErrors["model"], "too long")
		assert.Contains(t, fieldErrors["device_token"], "too long")
		assert.Contains(t, fieldErrors["device_identifier"], "too long")
	})

	t.Run("Platform validation for create", func(t *testing.T) {
		data := DeviceData{
			Name:                 "My Device",
			Platform:             Platform("invalid_platform"),
			OsVersion:            "14.0",
			Model:                "Test Model",
			DeviceToken:          "token",
			NotificationsEnabled: true,
			DeviceIdentifier:     "device123",
		}

		errors := data.Validate(false) // isUpdate = false
		assert.NotEmpty(t, errors)

		// Should have platform validation error
		hasPlatformError := false
		for _, err := range errors {
			if err.Field == "platform" {
				hasPlatformError = true
				break
			}
		}
		assert.True(t, hasPlatformError)
	})

	t.Run("Edge case - exact length limits", func(t *testing.T) {
		data := DeviceData{
			Name:                 strings.Repeat("a", 255), // Exactly at limit
			Platform:             PlatformIOS,
			OsVersion:            strings.Repeat("b", 50),  // Exactly at limit
			Model:                strings.Repeat("c", 100), // Exactly at limit
			DeviceToken:          strings.Repeat("d", 255), // Exactly at limit
			NotificationsEnabled: true,
			DeviceIdentifier:     strings.Repeat("e", 50), // Exactly at limit
		}

		errors := data.Validate(false)
		assert.Empty(t, errors) // Should be valid at exact limits
	})
}
