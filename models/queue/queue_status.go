package queue

import (
	"fmt"
)

type Status string

const (
	Created    Status = "created"
	Queued     Status = "queued"
	Processing Status = "processing"
	Completed  Status = "completed"
	Failed     Status = "failed"
	Retry      Status = "retry"
	Paused     Status = "paused"
	Cancelled  Status = "cancelled"
	Skipped    Status = "skipped"
	Delayed    Status = "delayed"
	Expired    Status = "expired"
)

var ValidStatuses = []Status{
	Created, Queued, Processing, Completed, Failed, Retry, Paused, Cancelled, Skipped, Delayed, Expired,
}

// Scan returns empty string for incorrect value
func (s *Status) Scan(value any) error {
	if value == nil {
		*s = "" // set default empty value for nil
		return nil
	}

	strValue, ok := value.(string)
	if !ok {
		return fmt.Errorf("scanning value of type %T into Status", value)
	}

	*s = Status(strValue)
	if !s.IsValidStatus() {
		return fmt.Errorf("validating Status value: %s", strValue)
	}

	return nil
}

func (s *Status) IsValidStatus() bool {
	for _, validStatus := range ValidStatuses {
		if *s == validStatus {
			return true
		}
	}
	return false
}

func (s *Status) String() string {
	return string(*s)
}
