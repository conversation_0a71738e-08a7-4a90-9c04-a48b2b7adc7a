package models

import (
	"api.appio.so/pkg"
	"encoding/json"
	"time"

	"github.com/appio-so/go-appioid"
)

const WidgetPrefix = "wgt"

type Widget struct {
	ID        *appioid.ID `json:"id"`
	ServiceID *appioid.ID `json:"service_id"`
	CreatedAt time.Time   `json:"-"`
	DeletedAt *time.Time  `json:"-"` // don't output in API
	WidgetRequest
}

type WidgetRequest struct {
	Template string       `json:"template"`
	Source   WidgetSource `json:"source"`
}

type WidgetSource struct {
	Type string          `json:"type"`
	Data json.RawMessage `json:"data"`
}

type WidgetConfig struct {
	ID        *appioid.ID `json:"id"`
	ServiceID *appioid.ID `json:"service_id"`
	Name      string      `json:"name"`
	Config    string      `json:"config"`
}

// User facing error
func (w WidgetRequest) Validate(isUpdate bool) *pkg.ValidationError {
	var errFields []pkg.ValidationField

	if !isUpdate && w.Template == "" {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "template",
			Reason: "template is required",
		})
	}
	if len(w.Template) > 10000 {
		errFields = append(errFields, pkg.ValidationField{
			Field:  "template",
			Reason: "template is too long (max 10000 characters)",
		})
	}

	// TODO: add WidgetSource validation?

	if len(errFields) > 0 {
		return &pkg.ValidationError{
			Entity: "widget",
			DocUrl: "https://docs.appio.so/#api-widgets",
			Fields: errFields,
		}
	}
	return nil
}
