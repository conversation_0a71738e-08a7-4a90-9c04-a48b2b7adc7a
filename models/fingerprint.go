package models

import (
	"fmt"
	"net"
	"time"

	"github.com/appio-so/go-appioid"
)

const FingerprintPrefix = "fing"

type Fingerprint struct {
	ID        *appioid.ID `json:"id"`
	CreatedAt time.Time   `json:"-"` // don't output in API
	FingerprintCreateRequest
	FingerprintData
}

type FingerprintMatchRequest struct {
	IP net.IP
	FingerprintData
}

func (f FingerprintMatchRequest) Validate() error {
	if f.IP == nil {
		return fmt.Errorf("ip is required")
	}
	if err := f.FingerprintData.Validate(); err != nil {
		return err
	}
	return nil
}

type FingerprintMatchResponse struct {
	FingerprintID  *appioid.ID `json:"fingerprint_id"`
	ServiceID      *appioid.ID `json:"service_id"`
	CustomerUserID string      `json:"customer_user_id"`
}

type FingerprintCreateRequest struct {
	IP             net.IP          `json:"ip"`
	ServiceID      *appioid.ID     `json:"service_id"` // WARNING: this has to be a pointer to be correctly encoded for DB
	CustomerUserID string          `json:"customer_user_id"`
	Data           FingerprintData `json:"data"` // `iOSVersion` & `model` are included in UserAgent
}

func (f FingerprintCreateRequest) Validate() error {
	if f.IP == nil {
		return fmt.Errorf("ip is required")
	}
	if f.ServiceID == nil {
		return fmt.Errorf("service_id is required")
	}
	if f.CustomerUserID == "" {
		return fmt.Errorf("customer_user_id is required")
	}
	if err := f.Data.Validate(); err != nil {
		return err
	}
	return nil
}

type FingerprintData struct {
	UserAgent        string `json:"user_agent"`
	ScreenResolution string `json:"screen_resolution"`
	Language         string `json:"language"`
	TimeOffset       int    `json:"time_offset"`
}

func (f FingerprintData) Validate() error {
	if f.UserAgent == "" {
		return fmt.Errorf("user_agent is required")
	}
	if f.ScreenResolution == "" {
		return fmt.Errorf("screen_resolution is required")
	}
	if f.Language == "" {
		return fmt.Errorf("language is required")
	}
	return nil
}
