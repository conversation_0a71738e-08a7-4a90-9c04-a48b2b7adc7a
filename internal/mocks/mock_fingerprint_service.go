// Code generated by MockGen. DO NOT EDIT.
// Source: services/fingerprint_service.go
//
// Generated by this command:
//
//	mockgen -source=services/fingerprint_service.go -destination=internal/mocks/mock_fingerprint_service.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockFingerprintServiceInterface is a mock of FingerprintServiceInterface interface.
type MockFingerprintServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockFingerprintServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockFingerprintServiceInterfaceMockRecorder is the mock recorder for MockFingerprintServiceInterface.
type MockFingerprintServiceInterfaceMockRecorder struct {
	mock *MockFingerprintServiceInterface
}

// NewMockFingerprintServiceInterface creates a new mock instance.
func NewMockFingerprintServiceInterface(ctrl *gomock.Controller) *MockFingerprintServiceInterface {
	mock := &MockFingerprintServiceInterface{ctrl: ctrl}
	mock.recorder = &MockFingerprintServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFingerprintServiceInterface) EXPECT() *MockFingerprintServiceInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockFingerprintServiceInterface) Create(ctx context.Context, createReq models.FingerprintCreateRequest) (*appioid.ID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, createReq)
	ret0, _ := ret[0].(*appioid.ID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockFingerprintServiceInterfaceMockRecorder) Create(ctx, createReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockFingerprintServiceInterface)(nil).Create), ctx, createReq)
}

// Match mocks base method.
func (m *MockFingerprintServiceInterface) Match(ctx context.Context, platform models.Platform, matchReq models.FingerprintMatchRequest) (*models.Fingerprint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Match", ctx, platform, matchReq)
	ret0, _ := ret[0].(*models.Fingerprint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Match indicates an expected call of Match.
func (mr *MockFingerprintServiceInterfaceMockRecorder) Match(ctx, platform, matchReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Match", reflect.TypeOf((*MockFingerprintServiceInterface)(nil).Match), ctx, platform, matchReq)
}
