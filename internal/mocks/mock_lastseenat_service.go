// Code generated by MockGen. DO NOT EDIT.
// Source: services/lastseenat_service.go
//
// Generated by this command:
//
//	mockgen -source=services/lastseenat_service.go -destination=internal/mocks/mock_lastseenat_service.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockLastSeenAtServiceInterface is a mock of LastSeenAtServiceInterface interface.
type MockLastSeenAtServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockLastSeenAtServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockLastSeenAtServiceInterfaceMockRecorder is the mock recorder for MockLastSeenAtServiceInterface.
type MockLastSeenAtServiceInterfaceMockRecorder struct {
	mock *MockLastSeenAtServiceInterface
}

// NewMockLastSeenAtServiceInterface creates a new mock instance.
func NewMockLastSeenAtServiceInterface(ctrl *gomock.Controller) *MockLastSeenAtServiceInterface {
	mock := &MockLastSeenAtServiceInterface{ctrl: ctrl}
	mock.recorder = &MockLastSeenAtServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLastSeenAtServiceInterface) EXPECT() *MockLastSeenAtServiceInterfaceMockRecorder {
	return m.recorder
}

// Update mocks base method.
func (m *MockLastSeenAtServiceInterface) Update(ctx context.Context, dvcID *appioid.ID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, dvcID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockLastSeenAtServiceInterfaceMockRecorder) Update(ctx, dvcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLastSeenAtServiceInterface)(nil).Update), ctx, dvcID)
}
