// Code generated by MockGen. DO NOT EDIT.
// Source: services/feedback_service.go
//
// Generated by this command:
//
//	mockgen -source=services/feedback_service.go -destination=internal/mocks/mock_feedback_service.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	net "net"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockFeedbackServiceInterface is a mock of FeedbackServiceInterface interface.
type MockFeedbackServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockFeedbackServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockFeedbackServiceInterfaceMockRecorder is the mock recorder for MockFeedbackServiceInterface.
type MockFeedbackServiceInterfaceMockRecorder struct {
	mock *MockFeedbackServiceInterface
}

// NewMockFeedbackServiceInterface creates a new mock instance.
func NewMockFeedbackServiceInterface(ctrl *gomock.Controller) *MockFeedbackServiceInterface {
	mock := &MockFeedbackServiceInterface{ctrl: ctrl}
	mock.recorder = &MockFeedbackServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFeedbackServiceInterface) EXPECT() *MockFeedbackServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateFeedback mocks base method.
func (m *MockFeedbackServiceInterface) CreateFeedback(ctx context.Context, platform models.Platform, version string, ip net.IP, svcID, dvcID *appioid.ID, message string) (*appioid.ID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateFeedback", ctx, platform, version, ip, svcID, dvcID, message)
	ret0, _ := ret[0].(*appioid.ID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateFeedback indicates an expected call of CreateFeedback.
func (mr *MockFeedbackServiceInterfaceMockRecorder) CreateFeedback(ctx, platform, version, ip, svcID, dvcID, message any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFeedback", reflect.TypeOf((*MockFeedbackServiceInterface)(nil).CreateFeedback), ctx, platform, version, ip, svcID, dvcID, message)
}
