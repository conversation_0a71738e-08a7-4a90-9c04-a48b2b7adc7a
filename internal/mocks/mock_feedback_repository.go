// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/feedback_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/feedback_repository.go -destination=internal/mocks/mock_feedback_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	net "net"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockFeedbackRepositoryInterface is a mock of FeedbackRepositoryInterface interface.
type MockFeedbackRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockFeedbackRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockFeedbackRepositoryInterfaceMockRecorder is the mock recorder for MockFeedbackRepositoryInterface.
type MockFeedbackRepositoryInterfaceMockRecorder struct {
	mock *MockFeedbackRepositoryInterface
}

// NewMockFeedbackRepositoryInterface creates a new mock instance.
func NewMockFeedbackRepositoryInterface(ctrl *gomock.Controller) *MockFeedbackRepositoryInterface {
	mock := &MockFeedbackRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockFeedbackRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFeedbackRepositoryInterface) EXPECT() *MockFeedbackRepositoryInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockFeedbackRepositoryInterface) Create(ctx context.Context, fdbID *appioid.ID, platform models.Platform, version string, ip net.IP, svcID, dvcID *appioid.ID, message string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, fdbID, platform, version, ip, svcID, dvcID, message)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockFeedbackRepositoryInterfaceMockRecorder) Create(ctx, fdbID, platform, version, ip, svcID, dvcID, message any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockFeedbackRepositoryInterface)(nil).Create), ctx, fdbID, platform, version, ip, svcID, dvcID, message)
}
