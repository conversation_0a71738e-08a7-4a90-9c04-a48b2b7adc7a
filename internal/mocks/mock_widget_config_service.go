// Code generated by MockGen. DO NOT EDIT.
// Source: services/widget_config_service.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	"api.appio.so/models"
	gomock "go.uber.org/mock/gomock"
)

// MockWidgetConfigServiceInterface is a mock of WidgetConfigServiceInterface interface.
type MockWidgetConfigServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockWidgetConfigServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockWidgetConfigServiceInterfaceMockRecorder is the mock recorder for MockWidgetConfigServiceInterface.
type MockWidgetConfigServiceInterfaceMockRecorder struct {
	mock *MockWidgetConfigServiceInterface
}

// NewMockWidgetConfigServiceInterface creates a new mock instance.
func NewMockWidgetConfigServiceInterface(ctrl *gomock.Controller) *MockWidgetConfigServiceInterface {
	mock := &MockWidgetConfigServiceInterface{ctrl: ctrl}
	mock.recorder = &MockWidgetConfigServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWidgetConfigServiceInterface) EXPECT() *MockWidgetConfigServiceInterfaceMockRecorder {
	return m.recorder
}

// ParseWidgetConfig mocks base method.
func (m *MockWidgetConfigServiceInterface) ParseWidgetConfig(platform models.Platform, widget *models.Widget) (*models.WidgetConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseWidgetConfig", platform, widget)
	ret0, _ := ret[0].(*models.WidgetConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ParseWidgetConfig indicates an expected call of ParseWidgetConfig.
func (mr *MockWidgetConfigServiceInterfaceMockRecorder) ParseWidgetConfig(platform, widget any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseWidgetConfig", reflect.TypeOf((*MockWidgetConfigServiceInterface)(nil).ParseWidgetConfig), platform, widget)
}
