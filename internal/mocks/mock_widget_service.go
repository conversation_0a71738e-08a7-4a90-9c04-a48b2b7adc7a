// Code generated by MockGen. DO NOT EDIT.
// Source: services/widget_service.go
//
// Generated by this command:
//
//	mockgen -source=services/widget_service.go -destination=internal/mocks/mock_widget_service.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockWidgetServiceInterface is a mock of WidgetServiceInterface interface.
type MockWidgetServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockWidgetServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockWidgetServiceInterfaceMockRecorder is the mock recorder for MockWidgetServiceInterface.
type MockWidgetServiceInterfaceMockRecorder struct {
	mock *MockWidgetServiceInterface
}

// NewMockWidgetServiceInterface creates a new mock instance.
func NewMockWidgetServiceInterface(ctrl *gomock.Controller) *MockWidgetServiceInterface {
	mock := &MockWidgetServiceInterface{ctrl: ctrl}
	mock.recorder = &MockWidgetServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWidgetServiceInterface) EXPECT() *MockWidgetServiceInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockWidgetServiceInterface) Create(ctx context.Context, svcID *appioid.ID, wgtReq models.WidgetRequest) (*appioid.ID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, svcID, wgtReq)
	ret0, _ := ret[0].(*appioid.ID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockWidgetServiceInterfaceMockRecorder) Create(ctx, svcID, wgtReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockWidgetServiceInterface)(nil).Create), ctx, svcID, wgtReq)
}

// Delete mocks base method.
func (m *MockWidgetServiceInterface) Delete(ctx context.Context, svcID, wgtID *appioid.ID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Deactivate", ctx, svcID, wgtID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockWidgetServiceInterfaceMockRecorder) Delete(ctx, svcID, wgtID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Deactivate", reflect.TypeOf((*MockWidgetServiceInterface)(nil).Delete), ctx, svcID, wgtID)
}

// FindByID mocks base method.
func (m *MockWidgetServiceInterface) FindByID(ctx context.Context, svcID, wgtID *appioid.ID) (*models.Widget, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, svcID, wgtID)
	ret0, _ := ret[0].(*models.Widget)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockWidgetServiceInterfaceMockRecorder) FindByID(ctx, svcID, wgtID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockWidgetServiceInterface)(nil).FindByID), ctx, svcID, wgtID)
}

// List mocks base method.
func (m *MockWidgetServiceInterface) List(ctx context.Context, svcID *appioid.ID) ([]models.Widget, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, svcID)
	ret0, _ := ret[0].([]models.Widget)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockWidgetServiceInterfaceMockRecorder) List(ctx, svcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockWidgetServiceInterface)(nil).List), ctx, svcID)
}

// Update mocks base method.
func (m *MockWidgetServiceInterface) Update(ctx context.Context, svcID, wgtID *appioid.ID, wgtReq models.WidgetRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, svcID, wgtID, wgtReq)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockWidgetServiceInterfaceMockRecorder) Update(ctx, svcID, wgtID, wgtReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockWidgetServiceInterface)(nil).Update), ctx, svcID, wgtID, wgtReq)
}
