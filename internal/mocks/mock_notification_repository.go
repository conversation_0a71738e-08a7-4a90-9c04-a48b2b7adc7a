// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/notification_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/notification_repository.go -destination=internal/mocks/mock_notification_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	notification_status "api.appio.so/models/notification_status"
	notification_type "api.appio.so/models/notification_type"
	appioid "github.com/appio-so/go-appioid"
	pgx "github.com/jackc/pgx/v5"
	gomock "go.uber.org/mock/gomock"
)

// MockNotificationRepositoryInterface is a mock of NotificationRepositoryInterface interface.
type MockNotificationRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockNotificationRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockNotificationRepositoryInterfaceMockRecorder is the mock recorder for MockNotificationRepositoryInterface.
type MockNotificationRepositoryInterfaceMockRecorder struct {
	mock *MockNotificationRepositoryInterface
}

// NewMockNotificationRepositoryInterface creates a new mock instance.
func NewMockNotificationRepositoryInterface(ctrl *gomock.Controller) *MockNotificationRepositoryInterface {
	mock := &MockNotificationRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockNotificationRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNotificationRepositoryInterface) EXPECT() *MockNotificationRepositoryInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockNotificationRepositoryInterface) Create(ctx context.Context, t notification_type.Type, ntfID, svcID *appioid.ID, ntfReq models.NotificationRequest, status notification_status.Status) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, t, ntfID, svcID, ntfReq, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockNotificationRepositoryInterfaceMockRecorder) Create(ctx, t, ntfID, svcID, ntfReq, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockNotificationRepositoryInterface)(nil).Create), ctx, t, ntfID, svcID, ntfReq, status)
}

// CreateTx mocks base method.
func (m *MockNotificationRepositoryInterface) CreateTx(tx pgx.Tx, ctx context.Context, t notification_type.Type, ntfID, svcID *appioid.ID, ntfReq models.NotificationRequest, status notification_status.Status) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTx", tx, ctx, t, ntfID, svcID, ntfReq, status)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTx indicates an expected call of CreateTx.
func (mr *MockNotificationRepositoryInterfaceMockRecorder) CreateTx(tx, ctx, t, ntfID, svcID, ntfReq, status any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTx", reflect.TypeOf((*MockNotificationRepositoryInterface)(nil).CreateTx), tx, ctx, t, ntfID, svcID, ntfReq, status)
}

// FindByID mocks base method.
func (m *MockNotificationRepositoryInterface) FindByID(ctx context.Context, svcID, ntfID *appioid.ID) (*models.NotificationResponseWithStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, svcID, ntfID)
	ret0, _ := ret[0].(*models.NotificationResponseWithStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockNotificationRepositoryInterfaceMockRecorder) FindByID(ctx, svcID, ntfID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockNotificationRepositoryInterface)(nil).FindByID), ctx, svcID, ntfID)
}

// List mocks base method.
func (m *MockNotificationRepositoryInterface) List(ctx context.Context, svcID *appioid.ID, statuses []notification_status.Status) ([]models.NotificationResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, svcID, statuses)
	ret0, _ := ret[0].([]models.NotificationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockNotificationRepositoryInterfaceMockRecorder) List(ctx, svcID, statuses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockNotificationRepositoryInterface)(nil).List), ctx, svcID, statuses)
}

// ListWithStats mocks base method.
func (m *MockNotificationRepositoryInterface) ListWithStats(ctx context.Context, svcID *appioid.ID, statuses []notification_status.Status) ([]models.NotificationResponseWithStats, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListWithStats", ctx, svcID, statuses)
	ret0, _ := ret[0].([]models.NotificationResponseWithStats)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListWithStats indicates an expected call of ListWithStats.
func (mr *MockNotificationRepositoryInterfaceMockRecorder) ListWithStats(ctx, svcID, statuses any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListWithStats", reflect.TypeOf((*MockNotificationRepositoryInterface)(nil).ListWithStats), ctx, svcID, statuses)
}
