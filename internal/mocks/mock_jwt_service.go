// Code generated by MockGen. DO NOT EDIT.
// Source: services/jwt_service.go
//
// Generated by this command:
//
//	mockgen -source=services/jwt_service.go -destination=internal/mocks/mock_jwt_service.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	roles "api.appio.so/pkg/roles"
	gomock "go.uber.org/mock/gomock"
)

// MockJWTServiceInterface is a mock of JWTServiceInterface interface.
type MockJWTServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockJWTServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockJWTServiceInterfaceMockRecorder is the mock recorder for MockJWTServiceInterface.
type MockJWTServiceInterfaceMockRecorder struct {
	mock *MockJWTServiceInterface
}

// NewMockJWTServiceInterface creates a new mock instance.
func NewMockJWTServiceInterface(ctrl *gomock.Controller) *MockJWTServiceInterface {
	mock := &MockJWTServiceInterface{ctrl: ctrl}
	mock.recorder = &MockJWTServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockJWTServiceInterface) EXPECT() *MockJWTServiceInterfaceMockRecorder {
	return m.recorder
}

// IsValidJWT mocks base method.
func (m *MockJWTServiceInterface) IsValidJWT(token string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsValidJWT", token)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsValidJWT indicates an expected call of IsValidJWT.
func (mr *MockJWTServiceInterfaceMockRecorder) IsValidJWT(token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsValidJWT", reflect.TypeOf((*MockJWTServiceInterface)(nil).IsValidJWT), token)
}

// ParseJWT mocks base method.
func (m *MockJWTServiceInterface) ParseJWT(ctx context.Context, token string) (*models.User, roles.Role, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ParseJWT", ctx, token)
	ret0, _ := ret[0].(*models.User)
	ret1, _ := ret[1].(roles.Role)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ParseJWT indicates an expected call of ParseJWT.
func (mr *MockJWTServiceInterfaceMockRecorder) ParseJWT(ctx, token any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ParseJWT", reflect.TypeOf((*MockJWTServiceInterface)(nil).ParseJWT), ctx, token)
}
