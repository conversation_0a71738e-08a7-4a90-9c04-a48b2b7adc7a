// Code generated by MockGen. DO NOT EDIT.
// Source: services/user_service.go
//
// Generated by this command:
//
//	mockgen -source=services/user_service.go -destination=internal/mocks/mock_user_service.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockUserServiceInterface is a mock of UserServiceInterface interface.
type MockUserServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockUserServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockUserServiceInterfaceMockRecorder is the mock recorder for MockUserServiceInterface.
type MockUserServiceInterfaceMockRecorder struct {
	mock *MockUserServiceInterface
}

// NewMockUserServiceInterface creates a new mock instance.
func NewMockUserServiceInterface(ctrl *gomock.Controller) *MockUserServiceInterface {
	mock := &MockUserServiceInterface{ctrl: ctrl}
	mock.recorder = &MockUserServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserServiceInterface) EXPECT() *MockUserServiceInterfaceMockRecorder {
	return m.recorder
}

// ValidateUserServiceAccess mocks base method.
func (m *MockUserServiceInterface) ValidateUserServiceAccess(ctx context.Context, userID, serviceID *appioid.ID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateUserServiceAccess", ctx, userID, serviceID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateUserServiceAccess indicates an expected call of ValidateUserServiceAccess.
func (mr *MockUserServiceInterfaceMockRecorder) ValidateUserServiceAccess(ctx, userID, serviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateUserServiceAccess", reflect.TypeOf((*MockUserServiceInterface)(nil).ValidateUserServiceAccess), ctx, userID, serviceID)
}
