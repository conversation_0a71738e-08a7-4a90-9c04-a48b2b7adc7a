// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/apikey_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/apikey_repository.go -destination=internal/mocks/mock_apikey_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockAPIKeyRepositoryInterface is a mock of APIKeyRepositoryInterface interface.
type MockAPIKeyRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockAPIKeyRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockAPIKeyRepositoryInterfaceMockRecorder is the mock recorder for MockAPIKeyRepositoryInterface.
type MockAPIKeyRepositoryInterfaceMockRecorder struct {
	mock *MockAPIKeyRepositoryInterface
}

// NewMockAPIKeyRepositoryInterface creates a new mock instance.
func NewMockAPIKeyRepositoryInterface(ctrl *gomock.Controller) *MockAPIKeyRepositoryInterface {
	mock := &MockAPIKeyRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockAPIKeyRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAPIKeyRepositoryInterface) EXPECT() *MockAPIKeyRepositoryInterfaceMockRecorder {
	return m.recorder
}

// FindBy mocks base method.
func (m *MockAPIKeyRepositoryInterface) FindBy(ctx context.Context, apiKey string) (*appioid.ID, *appioid.ID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindBy", ctx, apiKey)
	ret0, _ := ret[0].(*appioid.ID)
	ret1, _ := ret[1].(*appioid.ID)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// FindBy indicates an expected call of FindBy.
func (mr *MockAPIKeyRepositoryInterfaceMockRecorder) FindBy(ctx, apiKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindBy", reflect.TypeOf((*MockAPIKeyRepositoryInterface)(nil).FindBy), ctx, apiKey)
}

// IsActiveAPIKey mocks base method.
func (m *MockAPIKeyRepositoryInterface) IsActiveAPIKey(ctx context.Context, apiKey string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsActiveAPIKey", ctx, apiKey)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsActiveAPIKey indicates an expected call of IsActiveAPIKey.
func (mr *MockAPIKeyRepositoryInterfaceMockRecorder) IsActiveAPIKey(ctx, apiKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsActiveAPIKey", reflect.TypeOf((*MockAPIKeyRepositoryInterface)(nil).IsActiveAPIKey), ctx, apiKey)
}
