// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/service_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/service_repository.go -destination=internal/mocks/mock_service_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockServiceRepositoryInterface is a mock of ServiceRepositoryInterface interface.
type MockServiceRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockServiceRepositoryInterfaceMockRecorder is the mock recorder for MockServiceRepositoryInterface.
type MockServiceRepositoryInterfaceMockRecorder struct {
	mock *MockServiceRepositoryInterface
}

// NewMockServiceRepositoryInterface creates a new mock instance.
func NewMockServiceRepositoryInterface(ctrl *gomock.Controller) *MockServiceRepositoryInterface {
	mock := &MockServiceRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockServiceRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceRepositoryInterface) EXPECT() *MockServiceRepositoryInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockServiceRepositoryInterface) Create(ctx context.Context, svcID, orgID *appioid.ID, svcReq models.ServiceRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, svcID, orgID, svcReq)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockServiceRepositoryInterfaceMockRecorder) Create(ctx, svcID, orgID, svcReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockServiceRepositoryInterface)(nil).Create), ctx, svcID, orgID, svcReq)
}

// FindByID mocks base method.
func (m *MockServiceRepositoryInterface) FindByID(ctx context.Context, svcID *appioid.ID) (*models.Service, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, svcID)
	ret0, _ := ret[0].(*models.Service)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockServiceRepositoryInterfaceMockRecorder) FindByID(ctx, svcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockServiceRepositoryInterface)(nil).FindByID), ctx, svcID)
}

// ListByDeviceID mocks base method.
func (m *MockServiceRepositoryInterface) ListByDeviceID(ctx context.Context, dvcID *appioid.ID) ([]models.Service, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByDeviceID", ctx, dvcID)
	ret0, _ := ret[0].([]models.Service)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByDeviceID indicates an expected call of ListByDeviceID.
func (mr *MockServiceRepositoryInterfaceMockRecorder) ListByDeviceID(ctx, dvcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByDeviceID", reflect.TypeOf((*MockServiceRepositoryInterface)(nil).ListByDeviceID), ctx, dvcID)
}

// ListByUserID mocks base method.
func (m *MockServiceRepositoryInterface) ListByUserID(ctx context.Context, usrID *appioid.ID) ([]models.Service, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByUserID", ctx, usrID)
	ret0, _ := ret[0].([]models.Service)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByUserID indicates an expected call of ListByUserID.
func (mr *MockServiceRepositoryInterfaceMockRecorder) ListByUserID(ctx, usrID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByUserID", reflect.TypeOf((*MockServiceRepositoryInterface)(nil).ListByUserID), ctx, usrID)
}

// Update mocks base method.
func (m *MockServiceRepositoryInterface) Update(ctx context.Context, svcID *appioid.ID, svcReq models.ServiceRequest) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, svcID, svcReq)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockServiceRepositoryInterfaceMockRecorder) Update(ctx, svcID, svcReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockServiceRepositoryInterface)(nil).Update), ctx, svcID, svcReq)
}
