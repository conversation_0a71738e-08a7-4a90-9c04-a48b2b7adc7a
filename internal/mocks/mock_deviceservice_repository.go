// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/deviceservice_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/deviceservice_repository.go -destination=internal/mocks/mock_deviceservice_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	pgx "github.com/jackc/pgx/v5"
	gomock "go.uber.org/mock/gomock"
)

// MockDeviceServiceRepositoryInterface is a mock of DeviceServiceRepositoryInterface interface.
type MockDeviceServiceRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDeviceServiceRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockDeviceServiceRepositoryInterfaceMockRecorder is the mock recorder for MockDeviceServiceRepositoryInterface.
type MockDeviceServiceRepositoryInterfaceMockRecorder struct {
	mock *MockDeviceServiceRepositoryInterface
}

// NewMockDeviceServiceRepositoryInterface creates a new mock instance.
func NewMockDeviceServiceRepositoryInterface(ctrl *gomock.Controller) *MockDeviceServiceRepositoryInterface {
	mock := &MockDeviceServiceRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockDeviceServiceRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeviceServiceRepositoryInterface) EXPECT() *MockDeviceServiceRepositoryInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockDeviceServiceRepositoryInterface) Create(ctx context.Context, dvcsvcID, dvcID, svcID *appioid.ID, customerUserID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, dvcsvcID, dvcID, svcID, customerUserID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockDeviceServiceRepositoryInterfaceMockRecorder) Create(ctx, dvcsvcID, dvcID, svcID, customerUserID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockDeviceServiceRepositoryInterface)(nil).Create), ctx, dvcsvcID, dvcID, svcID, customerUserID)
}

// CreateTx mocks base method.
func (m *MockDeviceServiceRepositoryInterface) CreateTx(tx pgx.Tx, ctx context.Context, dvcsvcID, dvcID, svcID *appioid.ID, customerUserID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTx", tx, ctx, dvcsvcID, dvcID, svcID, customerUserID)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTx indicates an expected call of CreateTx.
func (mr *MockDeviceServiceRepositoryInterfaceMockRecorder) CreateTx(tx, ctx, dvcsvcID, dvcID, svcID, customerUserID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTx", reflect.TypeOf((*MockDeviceServiceRepositoryInterface)(nil).CreateTx), tx, ctx, dvcsvcID, dvcID, svcID, customerUserID)
}

// Deactivate mocks base method.
func (m *MockDeviceServiceRepositoryInterface) Deactivate(ctx context.Context, svcID, dvcID *appioid.ID) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Deactivate", ctx, svcID, dvcID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Deactivate indicates an expected call of Deactivate.
func (mr *MockDeviceServiceRepositoryInterfaceMockRecorder) Deactivate(ctx, svcID, dvcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Deactivate", reflect.TypeOf((*MockDeviceServiceRepositoryInterface)(nil).Deactivate), ctx, svcID, dvcID)
}

// DeactivateByCustomerUserID mocks base method.
func (m *MockDeviceServiceRepositoryInterface) DeactivateByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]appioid.ID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeactivateByCustomerUserID", ctx, svcID, customerUserID)
	ret0, _ := ret[0].([]appioid.ID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeactivateByCustomerUserID indicates an expected call of DeactivateByCustomerUserID.
func (mr *MockDeviceServiceRepositoryInterfaceMockRecorder) DeactivateByCustomerUserID(ctx, svcID, customerUserID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeactivateByCustomerUserID", reflect.TypeOf((*MockDeviceServiceRepositoryInterface)(nil).DeactivateByCustomerUserID), ctx, svcID, customerUserID)
}

// UpdateTx mocks base method.
func (m *MockDeviceServiceRepositoryInterface) UpdateTx(tx pgx.Tx, ctx context.Context, svcID, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTx", tx, ctx, svcID, dvcID, dvcUpdateReq)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTx indicates an expected call of UpdateTx.
func (mr *MockDeviceServiceRepositoryInterfaceMockRecorder) UpdateTx(tx, ctx, svcID, dvcID, dvcUpdateReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTx", reflect.TypeOf((*MockDeviceServiceRepositoryInterface)(nil).UpdateTx), tx, ctx, svcID, dvcID, dvcUpdateReq)
}
