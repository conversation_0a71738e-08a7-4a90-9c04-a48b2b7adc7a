// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/user_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/user_repository.go -destination=internal/mocks/mock_user_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockUserRepositoryInterface is a mock of UserRepositoryInterface interface.
type MockUserRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockUserRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockUserRepositoryInterfaceMockRecorder is the mock recorder for MockUserRepositoryInterface.
type MockUserRepositoryInterfaceMockRecorder struct {
	mock *MockUserRepositoryInterface
}

// NewMockUserRepositoryInterface creates a new mock instance.
func NewMockUserRepositoryInterface(ctrl *gomock.Controller) *MockUserRepositoryInterface {
	mock := &MockUserRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockUserRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUserRepositoryInterface) EXPECT() *MockUserRepositoryInterfaceMockRecorder {
	return m.recorder
}

// ValidateUserServiceAccess mocks base method.
func (m *MockUserRepositoryInterface) ValidateUserServiceAccess(ctx context.Context, userID, serviceID *appioid.ID) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateUserServiceAccess", ctx, userID, serviceID)
	ret0, _ := ret[0].(bool)
	return ret0
}

// ValidateUserServiceAccess indicates an expected call of ValidateUserServiceAccess.
func (mr *MockUserRepositoryInterfaceMockRecorder) ValidateUserServiceAccess(ctx, userID, serviceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateUserServiceAccess", reflect.TypeOf((*MockUserRepositoryInterface)(nil).ValidateUserServiceAccess), ctx, userID, serviceID)
}
