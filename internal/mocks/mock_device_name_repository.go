// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/device_name_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/device_name_repository.go -destination=internal/mocks/mock_device_name_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	gomock "go.uber.org/mock/gomock"
)

// MockDeviceNameRepositoryInterface is a mock of DeviceNameRepositoryInterface interface.
type MockDeviceNameRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDeviceNameRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockDeviceNameRepositoryInterfaceMockRecorder is the mock recorder for MockDeviceNameRepositoryInterface.
type MockDeviceNameRepositoryInterfaceMockRecorder struct {
	mock *MockDeviceNameRepositoryInterface
}

// NewMockDeviceNameRepositoryInterface creates a new mock instance.
func NewMockDeviceNameRepositoryInterface(ctrl *gomock.Controller) *MockDeviceNameRepositoryInterface {
	mock := &MockDeviceNameRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockDeviceNameRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeviceNameRepositoryInterface) EXPECT() *MockDeviceNameRepositoryInterfaceMockRecorder {
	return m.recorder
}

// FindMarketingName mocks base method.
func (m *MockDeviceNameRepositoryInterface) FindMarketingName(ctx context.Context, brand, model string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindMarketingName", ctx, brand, model)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindMarketingName indicates an expected call of FindMarketingName.
func (mr *MockDeviceNameRepositoryInterfaceMockRecorder) FindMarketingName(ctx, brand, model any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindMarketingName", reflect.TypeOf((*MockDeviceNameRepositoryInterface)(nil).FindMarketingName), ctx, brand, model)
}

// Import mocks base method.
func (m *MockDeviceNameRepositoryInterface) Import(ctx context.Context, deviceNames []models.DeviceName) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Import", ctx, deviceNames)
	ret0, _ := ret[0].(error)
	return ret0
}

// Import indicates an expected call of Import.
func (mr *MockDeviceNameRepositoryInterfaceMockRecorder) Import(ctx, deviceNames any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Import", reflect.TypeOf((*MockDeviceNameRepositoryInterface)(nil).Import), ctx, deviceNames)
}
