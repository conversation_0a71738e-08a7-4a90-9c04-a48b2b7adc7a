// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/device_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/device_repository.go -destination=internal/mocks/mock_device_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	pgx "github.com/jackc/pgx/v5"
	gomock "go.uber.org/mock/gomock"
)

// MockDeviceRepositoryInterface is a mock of DeviceRepositoryInterface interface.
type MockDeviceRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDeviceRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockDeviceRepositoryInterfaceMockRecorder is the mock recorder for MockDeviceRepositoryInterface.
type MockDeviceRepositoryInterfaceMockRecorder struct {
	mock *MockDeviceRepositoryInterface
}

// NewMockDeviceRepositoryInterface creates a new mock instance.
func NewMockDeviceRepositoryInterface(ctrl *gomock.Controller) *MockDeviceRepositoryInterface {
	mock := &MockDeviceRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockDeviceRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeviceRepositoryInterface) EXPECT() *MockDeviceRepositoryInterfaceMockRecorder {
	return m.recorder
}

// CreateTx mocks base method.
func (m *MockDeviceRepositoryInterface) CreateTx(tx pgx.Tx, ctx context.Context, dvcID *appioid.ID, dvcReq models.DeviceCreateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateTx", tx, ctx, dvcID, dvcReq)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateTx indicates an expected call of CreateTx.
func (mr *MockDeviceRepositoryInterfaceMockRecorder) CreateTx(tx, ctx, dvcID, dvcReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTx", reflect.TypeOf((*MockDeviceRepositoryInterface)(nil).CreateTx), tx, ctx, dvcID, dvcReq)
}

// FindByCustomerUserID mocks base method.
func (m *MockDeviceRepositoryInterface) FindByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]models.DeviceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByCustomerUserID", ctx, svcID, customerUserID)
	ret0, _ := ret[0].([]models.DeviceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByCustomerUserID indicates an expected call of FindByCustomerUserID.
func (mr *MockDeviceRepositoryInterfaceMockRecorder) FindByCustomerUserID(ctx, svcID, customerUserID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByCustomerUserID", reflect.TypeOf((*MockDeviceRepositoryInterface)(nil).FindByCustomerUserID), ctx, svcID, customerUserID)
}

// FindByID mocks base method.
func (m *MockDeviceRepositoryInterface) FindByID(ctx context.Context, svcID, dvcID *appioid.ID) (*models.DeviceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, svcID, dvcID)
	ret0, _ := ret[0].(*models.DeviceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockDeviceRepositoryInterfaceMockRecorder) FindByID(ctx, svcID, dvcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockDeviceRepositoryInterface)(nil).FindByID), ctx, svcID, dvcID)
}

// List mocks base method.
func (m *MockDeviceRepositoryInterface) List(ctx context.Context, svcID *appioid.ID) ([]models.DeviceRecord, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, svcID)
	ret0, _ := ret[0].([]models.DeviceRecord)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockDeviceRepositoryInterfaceMockRecorder) List(ctx, svcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockDeviceRepositoryInterface)(nil).List), ctx, svcID)
}

// Update mocks base method.
func (m *MockDeviceRepositoryInterface) Update(ctx context.Context, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, dvcID, dvcUpdateReq)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockDeviceRepositoryInterfaceMockRecorder) Update(ctx, dvcID, dvcUpdateReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockDeviceRepositoryInterface)(nil).Update), ctx, dvcID, dvcUpdateReq)
}

// UpdateLastSeenAt mocks base method.
func (m *MockDeviceRepositoryInterface) UpdateLastSeenAt(ctx context.Context, dvcID *appioid.ID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLastSeenAt", ctx, dvcID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLastSeenAt indicates an expected call of UpdateLastSeenAt.
func (mr *MockDeviceRepositoryInterfaceMockRecorder) UpdateLastSeenAt(ctx, dvcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLastSeenAt", reflect.TypeOf((*MockDeviceRepositoryInterface)(nil).UpdateLastSeenAt), ctx, dvcID)
}

// UpdateTx mocks base method.
func (m *MockDeviceRepositoryInterface) UpdateTx(tx pgx.Tx, ctx context.Context, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTx", tx, ctx, dvcID, dvcUpdateReq)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTx indicates an expected call of UpdateTx.
func (mr *MockDeviceRepositoryInterfaceMockRecorder) UpdateTx(tx, ctx, dvcID, dvcUpdateReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTx", reflect.TypeOf((*MockDeviceRepositoryInterface)(nil).UpdateTx), tx, ctx, dvcID, dvcUpdateReq)
}
