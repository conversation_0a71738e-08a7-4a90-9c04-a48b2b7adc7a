// Code generated by MockGen. DO NOT EDIT.
// Source: services/device_service.go
//
// Generated by this command:
//
//	mockgen -source=services/device_service.go -destination=internal/mocks/mock_device_service.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockDeviceServiceInterface is a mock of DeviceServiceInterface interface.
type MockDeviceServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDeviceServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockDeviceServiceInterfaceMockRecorder is the mock recorder for MockDeviceServiceInterface.
type MockDeviceServiceInterfaceMockRecorder struct {
	mock *MockDeviceServiceInterface
}

// NewMockDeviceServiceInterface creates a new mock instance.
func NewMockDeviceServiceInterface(ctrl *gomock.Controller) *MockDeviceServiceInterface {
	mock := &MockDeviceServiceInterface{ctrl: ctrl}
	mock.recorder = &MockDeviceServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeviceServiceInterface) EXPECT() *MockDeviceServiceInterfaceMockRecorder {
	return m.recorder
}

// CreateAndLink mocks base method.
func (m *MockDeviceServiceInterface) CreateAndLink(ctx context.Context, svcID *appioid.ID, dvcReq models.DeviceCreateRequest) (*appioid.ID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAndLink", ctx, svcID, dvcReq)
	ret0, _ := ret[0].(*appioid.ID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAndLink indicates an expected call of CreateAndLink.
func (mr *MockDeviceServiceInterfaceMockRecorder) CreateAndLink(ctx, svcID, dvcReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAndLink", reflect.TypeOf((*MockDeviceServiceInterface)(nil).CreateAndLink), ctx, svcID, dvcReq)
}

// Deactivate mocks base method.
func (m *MockDeviceServiceInterface) Deactivate(ctx context.Context, svcID, dvcID *appioid.ID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Deactivate", ctx, svcID, dvcID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Deactivate indicates an expected call of Deactivate.
func (mr *MockDeviceServiceInterfaceMockRecorder) Deactivate(ctx, svcID, dvcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Deactivate", reflect.TypeOf((*MockDeviceServiceInterface)(nil).Deactivate), ctx, svcID, dvcID)
}

// DeactivateByCustomerUserID mocks base method.
func (m *MockDeviceServiceInterface) DeactivateByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]appioid.ID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeactivateByCustomerUserID", ctx, svcID, customerUserID)
	ret0, _ := ret[0].([]appioid.ID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeactivateByCustomerUserID indicates an expected call of DeactivateByCustomerUserID.
func (mr *MockDeviceServiceInterfaceMockRecorder) DeactivateByCustomerUserID(ctx, svcID, customerUserID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeactivateByCustomerUserID", reflect.TypeOf((*MockDeviceServiceInterface)(nil).DeactivateByCustomerUserID), ctx, svcID, customerUserID)
}

// FindByCustomerUserID mocks base method.
func (m *MockDeviceServiceInterface) FindByCustomerUserID(ctx context.Context, svcID *appioid.ID, customerUserID string) ([]models.DeviceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByCustomerUserID", ctx, svcID, customerUserID)
	ret0, _ := ret[0].([]models.DeviceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByCustomerUserID indicates an expected call of FindByCustomerUserID.
func (mr *MockDeviceServiceInterfaceMockRecorder) FindByCustomerUserID(ctx, svcID, customerUserID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByCustomerUserID", reflect.TypeOf((*MockDeviceServiceInterface)(nil).FindByCustomerUserID), ctx, svcID, customerUserID)
}

// FindByID mocks base method.
func (m *MockDeviceServiceInterface) FindByID(ctx context.Context, svcID, dvcID *appioid.ID) (*models.DeviceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByID", ctx, svcID, dvcID)
	ret0, _ := ret[0].(*models.DeviceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByID indicates an expected call of FindByID.
func (mr *MockDeviceServiceInterfaceMockRecorder) FindByID(ctx, svcID, dvcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByID", reflect.TypeOf((*MockDeviceServiceInterface)(nil).FindByID), ctx, svcID, dvcID)
}

// LinkWithService mocks base method.
func (m *MockDeviceServiceInterface) LinkWithService(ctx context.Context, svcID, dvcID *appioid.ID, dvcLinkReq models.DeviceLinkServiceRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkWithService", ctx, svcID, dvcID, dvcLinkReq)
	ret0, _ := ret[0].(error)
	return ret0
}

// LinkWithService indicates an expected call of LinkWithService.
func (mr *MockDeviceServiceInterfaceMockRecorder) LinkWithService(ctx, svcID, dvcID, dvcLinkReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkWithService", reflect.TypeOf((*MockDeviceServiceInterface)(nil).LinkWithService), ctx, svcID, dvcID, dvcLinkReq)
}

// List mocks base method.
func (m *MockDeviceServiceInterface) List(ctx context.Context, svcID *appioid.ID) ([]models.DeviceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, svcID)
	ret0, _ := ret[0].([]models.DeviceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockDeviceServiceInterfaceMockRecorder) List(ctx, svcID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockDeviceServiceInterface)(nil).List), ctx, svcID)
}

// Update mocks base method.
func (m *MockDeviceServiceInterface) Update(ctx context.Context, dvcID *appioid.ID, dvcUpdateReq models.DeviceUpdateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, dvcID, dvcUpdateReq)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockDeviceServiceInterfaceMockRecorder) Update(ctx, dvcID, dvcUpdateReq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockDeviceServiceInterface)(nil).Update), ctx, dvcID, dvcUpdateReq)
}
