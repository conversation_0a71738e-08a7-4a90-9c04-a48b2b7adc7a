// Code generated by MockGen. DO NOT EDIT.
// Source: repositories/fingerprint_repository.go
//
// Generated by this command:
//
//	mockgen -source=repositories/fingerprint_repository.go -destination=internal/mocks/mock_fingerprint_repository.go -package=mocks
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "api.appio.so/models"
	appioid "github.com/appio-so/go-appioid"
	gomock "go.uber.org/mock/gomock"
)

// MockFingerprintRepositoryInterface is a mock of FingerprintRepositoryInterface interface.
type MockFingerprintRepositoryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockFingerprintRepositoryInterfaceMockRecorder
	isgomock struct{}
}

// MockFingerprintRepositoryInterfaceMockRecorder is the mock recorder for MockFingerprintRepositoryInterface.
type MockFingerprintRepositoryInterfaceMockRecorder struct {
	mock *MockFingerprintRepositoryInterface
}

// NewMockFingerprintRepositoryInterface creates a new mock instance.
func NewMockFingerprintRepositoryInterface(ctrl *gomock.Controller) *MockFingerprintRepositoryInterface {
	mock := &MockFingerprintRepositoryInterface{ctrl: ctrl}
	mock.recorder = &MockFingerprintRepositoryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFingerprintRepositoryInterface) EXPECT() *MockFingerprintRepositoryInterfaceMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockFingerprintRepositoryInterface) Create(ctx context.Context, fingID *appioid.ID, freq models.FingerprintCreateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, fingID, freq)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockFingerprintRepositoryInterfaceMockRecorder) Create(ctx, fingID, freq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockFingerprintRepositoryInterface)(nil).Create), ctx, fingID, freq)
}

// ListBy mocks base method.
func (m *MockFingerprintRepositoryInterface) ListBy(ctx context.Context, freq models.FingerprintMatchRequest) ([]models.Fingerprint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBy", ctx, freq)
	ret0, _ := ret[0].([]models.Fingerprint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBy indicates an expected call of ListBy.
func (mr *MockFingerprintRepositoryInterfaceMockRecorder) ListBy(ctx, freq any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBy", reflect.TypeOf((*MockFingerprintRepositoryInterface)(nil).ListBy), ctx, freq)
}
