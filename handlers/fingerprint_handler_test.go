package handlers

import (
	"bytes"
	"api.appio.so/models"
	"encoding/json"
	"net"
	"net/http"
	"net/http/httptest"
	"testing"

	"api.appio.so/internal/mocks"
	"api.appio.so/pkg"
	"github.com/appio-so/go-appioid"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"go.uber.org/zap/zaptest"
)

func setupFingerprintHandler(t *testing.T) (*FingerprintHandler, *mocks.MockFingerprintServiceInterface, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	mockService := mocks.NewMockFingerprintServiceInterface(ctrl)
	logger := zaptest.NewLogger(t)

	handler := NewFingerprintHandler(mockService, logger)
	return handler, mockService, ctrl
}

func TestNewFingerprintHandler(t *testing.T) {
	t.Run("Creates fingerprint handler correctly", func(t *testing.T) {
		handler, _, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		assert.NotNil(t, handler)
		assert.NotNil(t, handler.service)
		assert.NotNil(t, handler.logger)
	})
}

func TestFingerprintHandler_Match(t *testing.T) {
	svcID := appioid.MustParse("svc_00000000000000000000000001")
	fingID := appioid.MustParse("fng_00000000000000000000000001")

	t.Run("Success - fingerprint matched", func(t *testing.T) {
		handler, mockService, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		matchReq := models.FingerprintMatchRequest{
			FingerprintData: models.FingerprintData{
				UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15",
				ScreenResolution: "1792x1120",
				Language:         "en-GB",
				TimeOffset:       0,
			},
		}

		expectedFingerprint := &models.Fingerprint{
			ID: fingID,
			FingerprintCreateRequest: models.FingerprintCreateRequest{
				ServiceID:      svcID,
				CustomerUserID: "user123",
			},
		}

		// The handler will set IP from request, so we expect the call with IP set
		expectedMatchReq := matchReq
		expectedMatchReq.IP = net.ParseIP("127.0.0.1") // Default for test env

		mockService.EXPECT().
			Match(gomock.Any(), models.PlatformIOS, expectedMatchReq).
			Return(expectedFingerprint, nil)

		reqBody, _ := json.Marshal(matchReq)
		req := httptest.NewRequest("POST", "/fingerprints/match", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = AddPlatformToContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		handler.Match(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.FingerprintMatchResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, fingID, response.FingerprintID)
		assert.Equal(t, svcID, response.ServiceID)
		assert.Equal(t, "user123", response.CustomerUserID)
	})

	t.Run("Success - Android platform", func(t *testing.T) {
		handler, mockService, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		svcID := appioid.MustParse("svc_00000000000000000000000001")
		fingID := appioid.MustParse("fp_00000000000000000000000001")
		matchReq := models.FingerprintMatchRequest{
			FingerprintData: models.FingerprintData{
				UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15",
				ScreenResolution: "1792x1120",
				Language:         "en-GB",
				TimeOffset:       0,
			},
		}

		expectedFingerprint := &models.Fingerprint{
			ID: fingID,
			FingerprintCreateRequest: models.FingerprintCreateRequest{
				ServiceID:      svcID,
				CustomerUserID: "user123",
			},
		}

		expectedMatchReq := matchReq
		expectedMatchReq.IP = net.ParseIP("127.0.0.1")

		mockService.EXPECT().
			Match(gomock.Any(), models.PlatformAndroid, expectedMatchReq).
			Return(expectedFingerprint, nil)

		reqBody, _ := json.Marshal(matchReq)
		req := httptest.NewRequest("POST", "/fingerprints/match", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = AddPlatformToContext(req, models.PlatformAndroid)
		w := httptest.NewRecorder()

		handler.Match(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response models.FingerprintMatchResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, fingID, response.FingerprintID)
		assert.Equal(t, svcID, response.ServiceID)
		assert.Equal(t, "user123", response.CustomerUserID)
	})

	t.Run("RawError - fingerprint not found", func(t *testing.T) {
		handler, mockService, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		matchReq := models.FingerprintMatchRequest{
			FingerprintData: models.FingerprintData{
				UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15",
				ScreenResolution: "1792x1120",
				Language:         "en-GB",
				TimeOffset:       0,
			},
		}

		expectedMatchReq := matchReq
		expectedMatchReq.IP = net.ParseIP("127.0.0.1")

		mockService.EXPECT().
			Match(gomock.Any(), models.PlatformIOS, expectedMatchReq).
			Return(nil, nil)

		reqBody, _ := json.Marshal(matchReq)
		req := httptest.NewRequest("POST", "/fingerprints/match", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = AddPlatformToContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		handler.Match(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		matchReq := models.FingerprintMatchRequest{
			FingerprintData: models.FingerprintData{
				UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15",
				ScreenResolution: "1792x1120",
				Language:         "en-GB",
				TimeOffset:       0,
			},
		}

		expectedMatchReq := matchReq
		expectedMatchReq.IP = net.ParseIP("127.0.0.1")

		mockService.EXPECT().
			Match(gomock.Any(), models.PlatformIOS, expectedMatchReq).
			Return(nil, pkg.ErrInternal)

		reqBody, _ := json.Marshal(matchReq)
		req := httptest.NewRequest("POST", "/fingerprints/match", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req = AddPlatformToContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		handler.Match(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		handler, _, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("POST", "/fingerprints/match", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		req = AddPlatformToContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		handler.Match(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("Success - with real IP from request", func(t *testing.T) {
		handler, mockService, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		matchReq := models.FingerprintMatchRequest{
			FingerprintData: models.FingerprintData{
				UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15",
				ScreenResolution: "1792x1120",
				Language:         "en-GB",
				TimeOffset:       0,
			},
		}

		expectedFingerprint := &models.Fingerprint{
			ID: fingID,
			FingerprintCreateRequest: models.FingerprintCreateRequest{
				ServiceID:      svcID,
				CustomerUserID: "user123",
			},
		}

		// The handler will set IP from request
		expectedMatchReq := matchReq
		expectedMatchReq.IP = net.ParseIP("***********")

		mockService.EXPECT().
			Match(gomock.Any(), models.PlatformIOS, expectedMatchReq).
			Return(expectedFingerprint, nil)

		reqBody, _ := json.Marshal(matchReq)
		req := httptest.NewRequest("POST", "/fingerprints/match", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Forwarded-For", "***********")
		req = AddPlatformToContext(req, models.PlatformIOS)
		w := httptest.NewRecorder()

		handler.Match(w, req)

		assert.Equal(t, http.StatusOK, w.Code)

		var response models.FingerprintMatchResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, fingID, response.FingerprintID)
	})
}

func TestFingerprintHandler_Create(t *testing.T) {
	fingID := appioid.MustParse("fng_00000000000000000000000001")
	svcID := appioid.MustParse("svc_00000000000000000000000001")

	t.Run("Success - fingerprint created", func(t *testing.T) {
		handler, mockService, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		createReq := models.FingerprintCreateRequest{
			ServiceID:      svcID,
			CustomerUserID: "user123",
			Data: models.FingerprintData{
				UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15",
				ScreenResolution: "1792x1120",
				Language:         "en-GB",
				TimeOffset:       0,
			},
		}

		// The handler will set IP if not provided
		expectedCreateReq := createReq
		expectedCreateReq.IP = net.ParseIP("127.0.0.1") // Default for test env

		mockService.EXPECT().
			Create(gomock.Any(), expectedCreateReq).
			Return(fingID, nil)

		reqBody, _ := json.Marshal(createReq)
		req := httptest.NewRequest("POST", "/fingerprints", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, fingID, response.ID)
	})

	t.Run("Success - fingerprint created with provided IP", func(t *testing.T) {
		handler, mockService, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		providedIP := net.ParseIP("***********00")
		createReq := models.FingerprintCreateRequest{
			IP:             providedIP,
			ServiceID:      svcID,
			CustomerUserID: "user123",
			Data: models.FingerprintData{
				UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15",
				ScreenResolution: "1792x1120",
				Language:         "en-GB",
				TimeOffset:       0,
			},
		}

		// The handler should keep the provided IP
		mockService.EXPECT().
			Create(gomock.Any(), createReq).
			Return(fingID, nil)

		reqBody, _ := json.Marshal(createReq)
		req := httptest.NewRequest("POST", "/fingerprints", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, fingID, response.ID)
	})

	t.Run("RawError - invalid JSON", func(t *testing.T) {
		handler, _, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		req := httptest.NewRequest("POST", "/fingerprints", bytes.NewBuffer([]byte("invalid json")))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - service error", func(t *testing.T) {
		handler, mockService, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		createReq := models.FingerprintCreateRequest{
			ServiceID:      svcID,
			CustomerUserID: "user123",
			Data: models.FingerprintData{
				UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15",
				ScreenResolution: "1792x1120",
				Language:         "en-GB",
				TimeOffset:       0,
			},
		}

		expectedCreateReq := createReq
		expectedCreateReq.IP = net.ParseIP("127.0.0.1")

		mockService.EXPECT().
			Create(gomock.Any(), expectedCreateReq).
			Return(nil, pkg.ErrInternal)

		reqBody, _ := json.Marshal(createReq)
		req := httptest.NewRequest("POST", "/fingerprints", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusInternalServerError, w.Code)
	})

	t.Run("Success - IP extraction from X-Forwarded-For", func(t *testing.T) {
		handler, mockService, ctrl := setupFingerprintHandler(t)
		defer ctrl.Finish()

		createReq := models.FingerprintCreateRequest{
			ServiceID:      svcID,
			CustomerUserID: "user123",
			Data: models.FingerprintData{
				UserAgent:        "Mozilla/5.0 (iPhone; CPU iPhone OS 18_1 like Mac OS X) AppleWebKit/605.1.15",
				ScreenResolution: "1792x1120",
				Language:         "en-GB",
				TimeOffset:       0,
			},
		}

		// The handler will extract IP from X-Forwarded-For header
		expectedCreateReq := createReq
		expectedCreateReq.IP = net.ParseIP("***********")

		mockService.EXPECT().
			Create(gomock.Any(), expectedCreateReq).
			Return(fingID, nil)

		reqBody, _ := json.Marshal(createReq)
		req := httptest.NewRequest("POST", "/fingerprints", bytes.NewBuffer(reqBody))
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("X-Forwarded-For", "***********")
		w := httptest.NewRecorder()

		handler.Create(w, req)

		assert.Equal(t, http.StatusCreated, w.Code)

		var response models.ResponseID
		err := json.Unmarshal(w.Body.Bytes(), &response)
		assert.NoError(t, err)
		assert.Equal(t, fingID, response.ID)
	})
}
