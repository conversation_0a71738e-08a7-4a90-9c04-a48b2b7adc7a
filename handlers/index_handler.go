package handlers

import (
	"fmt"
	"net/http"
)

func IndexHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		url := "https://docs.appio.so/"

		w.Header().Set("Location", url)
		w.WriteHeader(http.StatusFound)

		w.<PERSON>er().Set("Content-Type", "text/html; charset=utf-8")
		fmt.Fprintf(w, `<!DOCTYPE html>
<html>
<head>
<meta http-equiv="refresh" content="0;url=%s">
</head>
<body>
Redirecting to <a href="%s">%s</a>
</body>
</html>`, url, url, url)
	}
}
