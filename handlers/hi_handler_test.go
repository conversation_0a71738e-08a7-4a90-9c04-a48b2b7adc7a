package handlers

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHiHandler(t *testing.T) {
	t.Run("Success - returns hi response", func(t *testing.T) {
		handler := HiHandler()
		req := httptest.NewRequest("GET", "/hi", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "👋")
		assert.Equal(t, "application/json", w.<PERSON><PERSON>().Get("Content-Type"))
		assert.NotEmpty(t, w.Body.String())
	})

	t.Run("Success - works with different HTTP methods", func(t *testing.T) {
		handler := HiHandler()
		methods := []string{"GET", "POST", "PUT"}

		for _, method := range methods {
			req := httptest.NewRequest(method, "/hi", nil)
			w := httptest.NewRecorder()

			handler(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
			assert.Contains(t, w.Body.String(), "👋")
		}
	})
}
