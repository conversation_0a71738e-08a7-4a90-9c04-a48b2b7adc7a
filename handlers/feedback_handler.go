package handlers

import (
	"api.appio.so/middlewares"
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/models"
	"api.appio.so/pkg"
	"api.appio.so/services"
	"go.uber.org/zap"
)

type FeedbackHandler struct {
	Handler

	service services.FeedbackServiceInterface
}

func NewFeedbackHandler(feedbackService services.FeedbackServiceInterface, logger *zap.Logger) *FeedbackHandler {
	return &FeedbackHandler{
		Handler: Handler{logger: logger},
		service: feedbackService,
	}
}

func (h *FeedbackHandler) Create(w http.ResponseWriter, r *http.Request) {
	platform, ok := h.GetPlatform(w, r)
	if !ok {
		return
	}

	// svcID can be null from intro screen
	svcID, _ := middlewares.GetServiceIDFromContext(r.Context())

	// dvcID can be null from intro screen
	dvcID, _ := middlewares.GetDeviceIDFromContext(r.Context())

	version := r.Header.Get("X-App-Version")
	if version == "" {
		h.logger.Error("missing X-App-Version header")
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	ip := h.GetIP(r)

	feedbackReq, err := helpers.DecodeJSON[models.FeedbackRequest](r)
	if err != nil {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	if verr := feedbackReq.Validate(); verr != nil {
		h.logger.Debug("invalid feedback request", zap.Any("data", feedbackReq), zap.Error(verr))
		helpers.RenderJSONError(w, r, pkg.NewUserFacingError(pkg.ErrInvalidInput, verr.Data()))
		return
	}

	ctx := r.Context()
	fdbID, err := h.service.CreateFeedback(ctx, platform, version, ip, svcID, dvcID, feedbackReq.Message)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	helpers.RenderJSON(w, r, &models.ResponseID{ID: fdbID}, http.StatusCreated)
}
