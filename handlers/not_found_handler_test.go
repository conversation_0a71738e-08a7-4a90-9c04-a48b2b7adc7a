package handlers

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNotFoundHandler(t *testing.T) {
	t.Run("Returns not found response", func(t *testing.T) {
		handler := NotFoundHandler()
		req := httptest.NewRequest("GET", "/nonexistent", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
		assert.Equal(t, "application/json", w.<PERSON>er().Get("Content-Type"))
		// The response should contain error information
		assert.NotEmpty(t, w.Body.String())
	})

	t.Run("Works with different HTTP methods", func(t *testing.T) {
		handler := NotFoundHandler()
		methods := []string{"GET", "POST", "PUT", "DELETE", "PATCH"}

		for _, method := range methods {
			t.Run("Method: "+method, func(t *testing.T) {
				req := httptest.NewRequest(method, "/nonexistent", nil)
				w := httptest.NewRecorder()

				handler(w, req)

				assert.Equal(t, http.StatusNotFound, w.Code)
				assert.NotEmpty(t, w.Body.String())
			})
		}
	})

	t.Run("Handler function is not nil", func(t *testing.T) {
		handler := NotFoundHandler()
		assert.NotNil(t, handler)
	})

	t.Run("Different URLs return same error", func(t *testing.T) {
		handler := NotFoundHandler()
		urls := []string{"/nonexistent", "/api/v1/missing", "/random/path"}

		for _, url := range urls {
			t.Run("URL: "+url, func(t *testing.T) {
				req := httptest.NewRequest("GET", url, nil)
				w := httptest.NewRecorder()

				handler(w, req)

				assert.Equal(t, http.StatusNotFound, w.Code)
				assert.NotEmpty(t, w.Body.String())
			})
		}
	})

	t.Run("Returns http.HandlerFunc type", func(t *testing.T) {
		handler := NotFoundHandler()
		assert.IsType(t, http.HandlerFunc(nil), handler)
	})

	t.Run("Sets Content-Type header correctly", func(t *testing.T) {
		handler := NotFoundHandler()
		req := httptest.NewRequest("GET", "/nonexistent", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		contentType := w.Header().Get("Content-Type")
		assert.Equal(t, "application/json", contentType)
	})

	t.Run("Response body contains error information", func(t *testing.T) {
		handler := NotFoundHandler()
		req := httptest.NewRequest("GET", "/nonexistent", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		body := w.Body.String()
		assert.NotEmpty(t, body)
		// Should contain some error-related content
		assert.True(t, len(body) > 0)
	})

	t.Run("Consistent response across multiple calls", func(t *testing.T) {
		handler := NotFoundHandler()

		var responses []string
		for i := 0; i < 3; i++ {
			req := httptest.NewRequest("GET", "/nonexistent", nil)
			w := httptest.NewRecorder()

			handler(w, req)

			assert.Equal(t, http.StatusNotFound, w.Code)
			responses = append(responses, w.Body.String())
		}

		// All responses should be identical
		for i := 1; i < len(responses); i++ {
			assert.Equal(t, responses[0], responses[i])
		}
	})

	t.Run("Works with complex URL paths", func(t *testing.T) {
		handler := NotFoundHandler()
		complexPaths := []string{
			"/api/v1/users/123/posts/456",
			"/public/assets/images/logo.png",
			"/very/deep/nested/path/that/does/not/exist",
		}

		for _, path := range complexPaths {
			t.Run("Path: "+path, func(t *testing.T) {
				req := httptest.NewRequest("GET", path, nil)
				w := httptest.NewRecorder()

				handler(w, req)

				assert.Equal(t, http.StatusNotFound, w.Code)
				assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
				assert.NotEmpty(t, w.Body.String())
			})
		}
	})

	t.Run("Works with query parameters", func(t *testing.T) {
		handler := NotFoundHandler()
		req := httptest.NewRequest("GET", "/nonexistent?param1=value1&param2=value2", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		assert.Equal(t, http.StatusNotFound, w.Code)
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
		assert.NotEmpty(t, w.Body.String())
	})
}
