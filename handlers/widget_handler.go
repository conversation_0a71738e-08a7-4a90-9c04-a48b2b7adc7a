package handlers

import (
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/models"
	"api.appio.so/pkg"
	"api.appio.so/services"
	"go.uber.org/zap"
)

type WidgetHandler struct {
	Handler

	service             services.WidgetServiceInterface
	widgetConfigService services.WidgetConfigServiceInterface
}

func NewWidgetHandler(widgetService services.WidgetServiceInterface, widgetConfigService services.WidgetConfigServiceInterface, logger *zap.Logger) *WidgetHandler {
	return &WidgetHandler{
		Handler:             Handler{logger: logger},
		service:             widgetService,
		widgetConfigService: widgetConfigService,
	}
}

func (h *WidgetHandler) Get(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	wgtID, ok := h.GetUrlID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	widget, err := h.service.FindByID(ctx, svcID, wgtID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	if widget == nil {
		helpers.RenderJSONError(w, r, pkg.ErrNotFound)
	} else {
		helpers.RenderJSON(w, r, widget, http.StatusOK)
	}
}

func (h *WidgetHandler) GetConfig(w http.ResponseWriter, r *http.Request) {
	platform, ok := h.GetPlatform(w, r)
	if !ok {
		return
	}

	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	wgtID, ok := h.GetUrlID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	widget, err := h.service.FindByID(ctx, svcID, wgtID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	widgetConfig, err := h.widgetConfigService.ParseWidgetConfig(platform, widget)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	helpers.RenderJSON(w, r, widgetConfig, http.StatusOK)
}

func (h *WidgetHandler) List(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	widgets, err := h.service.List(ctx, svcID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	helpers.RenderJSON(w, r, widgets, http.StatusOK)
}

func (h *WidgetHandler) Create(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	wgtReq, err := helpers.DecodeJSON[models.WidgetRequest](r)
	if err != nil {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	if verr := wgtReq.Validate(false); verr != nil {
		h.logger.Debug("invalid widget create request", zap.Any("data", wgtReq), zap.Error(verr))
		helpers.RenderJSONError(w, r, pkg.NewUserFacingError(pkg.ErrInvalidInput, verr.Data()))
		return
	}

	ctx := r.Context()
	wgtID, err := h.service.Create(ctx, svcID, wgtReq)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	helpers.RenderJSON(w, r, &models.ResponseID{ID: wgtID}, http.StatusCreated)
}

func (h *WidgetHandler) Update(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	wgtID, ok := h.GetUrlID(w, r)
	if !ok {
		return
	}

	wgtReq, err := helpers.DecodeJSON[models.WidgetRequest](r)
	if err != nil {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	if verr := wgtReq.Validate(true); verr != nil {
		h.logger.Debug("invalid widget update request", zap.Any("data", wgtReq), zap.Error(verr))
		helpers.RenderJSONError(w, r, pkg.NewUserFacingError(pkg.ErrInvalidInput, verr.Data()))
		return
	}

	ctx := r.Context()
	if err := h.service.Update(ctx, svcID, wgtID, wgtReq); err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	helpers.RenderJSON(w, r, &models.ResponseID{ID: wgtID}, http.StatusOK)
}

func (h *WidgetHandler) Delete(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	wgtID, ok := h.GetUrlID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	err := h.service.Delete(ctx, svcID, wgtID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	helpers.RenderJSON(w, r, &models.ResponseID{ID: wgtID}, http.StatusOK)
}
