package handlers

import (
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/models"
	"api.appio.so/pkg"
	"api.appio.so/services"
	"go.uber.org/zap"
)

type FingerprintHandler struct {
	Handler

	service services.FingerprintServiceInterface
}

func NewFingerprintHandler(fingerprintService services.FingerprintServiceInterface, logger *zap.Logger) *FingerprintHandler {
	return &FingerprintHandler{
		Handler: Handler{logger: logger},
		service: fingerprintService,
	}
}

func (h *FingerprintHandler) Match(w http.ResponseWriter, r *http.Request) {
	matchReq, err := helpers.DecodeJSON[models.FingerprintMatchRequest](r)
	if err != nil {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	// Requested directly from app
	matchReq.IP = h.GetIP(r)

	if err := matchReq.Validate(); err != nil {
		h.logger.Debug("invalid fingerprint match request", zap.Any("data", matchReq), zap.Error(err))
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	platform, ok := h.GetPlatform(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	fing, err := h.service.Match(ctx, platform, matchReq)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	// Check for nil just in case even if unnecessary
	if fing == nil {
		helpers.RenderJSONError(w, r, pkg.ErrNotFound)
		return
	}

	res := models.FingerprintMatchResponse{
		FingerprintID:  fing.ID,
		ServiceID:      fing.ServiceID,
		CustomerUserID: fing.CustomerUserID,
	}
	helpers.RenderJSON(w, r, res, http.StatusOK)
}

func (h *FingerprintHandler) Create(w http.ResponseWriter, r *http.Request) {
	fingReq, err := helpers.DecodeJSON[models.FingerprintCreateRequest](r)
	if err != nil {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	// Requested from app.appio.so with IP. This is a fallback for direct requests.
	if fingReq.IP == nil {
		fingReq.IP = h.GetIP(r)
	}

	if err := fingReq.Validate(); err != nil {
		h.logger.Debug("invalid fingerprint create request", zap.Any("data", fingReq), zap.Error(err))
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	ctx := r.Context()
	ID, err := h.service.Create(ctx, fingReq)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	helpers.RenderJSON(w, r, &models.ResponseID{ID: ID}, http.StatusCreated)
}
