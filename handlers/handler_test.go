package handlers

import (
	"api.appio.so/middlewares"
	"context"
	"github.com/appio-so/go-appioid"
	"github.com/go-chi/chi/v5"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestHandler_GetServiceID(t *testing.T) {
	logger := zaptest.NewLogger(t)
	handler := &Handler{logger: logger}

	t.Run("Success - service ID found in context", func(t *testing.T) {
		svcID := appioid.MustParse("svc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), middlewares.SvcIDKey{}, svcID)
		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		result, ok := handler.GetServiceID(w, req)

		assert.True(t, ok)
		assert.NotNil(t, result)
		assert.Equal(t, svcID, result)
		assert.Equal(t, http.StatusOK, w.Code) // No error response written
	})

	t.Run("RawError - service ID not found in context", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		result, ok := handler.GetServiceID(w, req)

		assert.False(t, ok)
		assert.Nil(t, result)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - wrong type in context", func(t *testing.T) {
		ctx := context.WithValue(context.Background(), middlewares.SvcIDKey{}, "invalid-type")
		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		result, ok := handler.GetServiceID(w, req)

		assert.False(t, ok)
		assert.Nil(t, result)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestHandler_GetDeviceID(t *testing.T) {
	logger := zaptest.NewLogger(t)
	handler := &Handler{logger: logger}

	t.Run("Success - device ID found in context", func(t *testing.T) {
		dvcID := appioid.MustParse("dvc_00000000000000000000000001")
		ctx := context.WithValue(context.Background(), middlewares.DvcIDKey{}, dvcID)
		req := httptest.NewRequest("GET", "/test", nil).WithContext(ctx)
		w := httptest.NewRecorder()

		result, ok := handler.GetDeviceID(w, req)

		assert.True(t, ok)
		assert.NotNil(t, result)
		assert.Equal(t, dvcID, result)
		assert.Equal(t, http.StatusOK, w.Code)
	})

	t.Run("RawError - device ID not found in context", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/test", nil)
		w := httptest.NewRecorder()

		result, ok := handler.GetDeviceID(w, req)

		assert.False(t, ok)
		assert.Nil(t, result)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})
}

func TestHandler_GetUrlID(t *testing.T) {
	logger := zaptest.NewLogger(t)
	handler := &Handler{logger: logger}

	t.Run("Success - valid ID in URL param", func(t *testing.T) {
		router := chi.NewRouter()
		var capturedID *appioid.ID
		var capturedOK bool

		router.Get("/test/{id}", func(w http.ResponseWriter, r *http.Request) {
			capturedID, capturedOK = handler.GetUrlID(w, r)
		})

		req := httptest.NewRequest("GET", "/test/svc_00000000000000000000000001", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.True(t, capturedOK)
		assert.NotNil(t, capturedID)
		assert.Equal(t, "svc_00000000000000000000000001", capturedID.String())
	})

	t.Run("RawError - invalid ID in URL param", func(t *testing.T) {
		router := chi.NewRouter()
		var capturedID *appioid.ID
		var capturedOK bool

		router.Get("/test/{id}", func(w http.ResponseWriter, r *http.Request) {
			capturedID, capturedOK = handler.GetUrlID(w, r)
		})

		req := httptest.NewRequest("GET", "/test/invalid-id", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.False(t, capturedOK)
		assert.Nil(t, capturedID)
		assert.Equal(t, http.StatusBadRequest, w.Code)
	})

	t.Run("RawError - empty ID in URL param", func(t *testing.T) {
		router := chi.NewRouter()
		var capturedID *appioid.ID
		var capturedOK bool

		router.Get("/test/{id}", func(w http.ResponseWriter, r *http.Request) {
			capturedID, capturedOK = handler.GetUrlID(w, r)
		})

		req := httptest.NewRequest("GET", "/test/", nil)
		w := httptest.NewRecorder()

		router.ServeHTTP(w, req)

		assert.False(t, capturedOK)
		assert.Nil(t, capturedID)
		// Empty path returns 404 from router, not 400 from handler
		assert.Equal(t, http.StatusNotFound, w.Code)
	})
}

func TestHandler_GetQueryID(t *testing.T) {
	logger := zaptest.NewLogger(t)
	handler := &Handler{logger: logger}

	t.Run("Success - valid ID in query param", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/test?device_id=dvc_00000000000000000000000001", nil)

		result, err := handler.GetQueryID(req, "device_id")

		assert.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "dvc_00000000000000000000000001", result.String())
	})

	t.Run("Success - empty query param returns nil", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/test", nil)

		result, err := handler.GetQueryID(req, "device_id")

		assert.NoError(t, err)
		assert.Nil(t, result)
	})

	t.Run("RawError - invalid ID in query param", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/test?device_id=invalid-id", nil)

		result, err := handler.GetQueryID(req, "device_id")

		assert.Error(t, err)
		assert.Nil(t, result)
	})

	t.Run("Success - missing query param returns nil", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/test?other_param=value", nil)

		result, err := handler.GetQueryID(req, "device_id")

		assert.NoError(t, err)
		assert.Nil(t, result)
	})
}

func TestHandler_ResponseIDsFrom(t *testing.T) {
	logger := zaptest.NewLogger(t)
	handler := &Handler{logger: logger}

	t.Run("Success - convert multiple IDs", func(t *testing.T) {
		id1 := appioid.MustParse("svc_00000000000000000000000001")
		id2 := appioid.MustParse("dvc_00000000000000000000000001")
		ids := []appioid.ID{*id1, *id2}

		result := handler.ResponseIDsFrom(ids)

		assert.Len(t, result, 2)
		assert.Equal(t, id1, result[0].ID)
		assert.Equal(t, id2, result[1].ID)
	})

	t.Run("Success - empty slice", func(t *testing.T) {
		ids := []appioid.ID{}

		result := handler.ResponseIDsFrom(ids)

		assert.Len(t, result, 0)
		assert.NotNil(t, result) // Should be empty slice, not nil
	})

	t.Run("Success - single ID", func(t *testing.T) {
		id := appioid.MustParse("ntf_00000000000000000000000001")
		ids := []appioid.ID{*id}

		result := handler.ResponseIDsFrom(ids)

		assert.Len(t, result, 1)
		assert.Equal(t, id, result[0].ID)
	})
}

func TestHandler_Constructor(t *testing.T) {
	t.Run("Handler struct creation", func(t *testing.T) {
		logger := zaptest.NewLogger(t)
		handler := &Handler{logger: logger}

		assert.NotNil(t, handler)
		assert.Equal(t, logger, handler.logger)
	})

	t.Run("Handler with nil logger", func(t *testing.T) {
		handler := &Handler{logger: nil}

		assert.NotNil(t, handler)
		assert.Nil(t, handler.logger)
	})
}
