package handlers

import (
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/models"
	"api.appio.so/pkg"
	"api.appio.so/services"
	"go.uber.org/zap"
)

type DeviceHandler struct {
	Handler

	service services.DeviceServiceInterface
}

func NewDeviceHandler(deviceService services.DeviceServiceInterface, logger *zap.Logger) *DeviceHandler {
	return &DeviceHandler{
		Handler: Handler{logger: logger},
		service: deviceService,
	}
}

func (h *DeviceHandler) Get(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	dvcID, ok := h.GetUrlID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	device, err := h.service.FindByID(ctx, svcID, dvcID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	if device == nil {
		helpers.RenderJSONError(w, r, pkg.ErrNotFound)
	} else {
		helpers.RenderJSON(w, r, device, http.StatusOK)
	}
}

func (h *DeviceHandler) List(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	devices, err := h.service.List(ctx, svcID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	helpers.RenderJSON(w, r, devices, http.StatusOK)
}

func (h *DeviceHandler) CreateAndLink(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	dvcReq, err := helpers.DecodeJSON[models.DeviceCreateRequest](r)
	if err != nil {
		h.logger.Error("error decoding device create and link request", zap.Error(err))
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	if verr := dvcReq.Validate(); verr != nil {
		h.logger.Debug("invalid device create request", zap.Any("data", dvcReq), zap.Error(verr))
		helpers.RenderJSONError(w, r, pkg.NewUserFacingError(pkg.ErrInvalidInput, verr.Data()))
		return
	}

	ctx := r.Context()
	dvcID, err := h.service.CreateAndLink(ctx, svcID, dvcReq)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	helpers.RenderJSON(w, r, &models.ResponseID{ID: dvcID}, http.StatusCreated)
}

func (h *DeviceHandler) Update(w http.ResponseWriter, r *http.Request) {
	dvcID, ok := h.GetUrlID(w, r)
	if !ok {
		return
	}

	dvcUpdateReq, err := helpers.DecodeJSON[models.DeviceUpdateRequest](r)
	if err != nil {
		h.logger.Error("error decoding device update request", zap.Error(err))
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	if verr := dvcUpdateReq.Validate(); verr != nil {
		h.logger.Debug("invalid device update request", zap.Any("data", dvcUpdateReq), zap.Error(verr))
		helpers.RenderJSONError(w, r, pkg.NewUserFacingError(pkg.ErrInvalidInput, verr.Data()))
		return
	}

	ctx := r.Context()
	if err = h.service.Update(ctx, dvcID, dvcUpdateReq); err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	helpers.RenderJSON(w, r, &models.ResponseID{ID: dvcID}, http.StatusOK)
}

func (h *DeviceHandler) LinkWithService(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	dvcID, ok := h.GetUrlID(w, r)
	if !ok {
		return
	}

	dvcLinkReq, err := helpers.DecodeJSON[models.DeviceLinkServiceRequest](r)
	if err != nil {
		h.logger.Error("error decoding link request", zap.Error(err))
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	if dvcLinkReq.CustomerUserID == "" {
		err := pkg.NewUserFacingError(pkg.ErrInvalidInput, map[string]any{
			"customer_user_id": "customer_user_id is required",
		})
		helpers.RenderJSONError(w, r, err)
		return
	}

	ctx := r.Context()
	if err := h.service.LinkWithService(ctx, svcID, dvcID, dvcLinkReq); err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	helpers.RenderJSON(w, r, &models.ResponseID{ID: dvcID}, http.StatusOK)
}

func (h *DeviceHandler) Deactivate(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	dvcID, ok := h.GetUrlID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	err := h.service.Deactivate(ctx, svcID, dvcID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	helpers.RenderJSON(w, r, &models.ResponseID{ID: dvcID}, http.StatusOK)
}

func (h *DeviceHandler) DeactivateByUser(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	customerUserID := r.URL.Query().Get("user_id")
	if customerUserID == "" {
		helpers.RenderJSONError(w, r, pkg.ErrNotFound)
		return
	}

	ctx := r.Context()
	dvcIDs, err := h.service.DeactivateByCustomerUserID(ctx, svcID, customerUserID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}

	helpers.RenderJSON(w, r, h.ResponseIDsFrom(dvcIDs), http.StatusOK)
}
