package handlers

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHealthCheckHandler(t *testing.T) {
	t.Run("Success - returns health check response", func(t *testing.T) {
		handler := HealthCheckHandler()
		req := httptest.NewRequest("GET", "/health-check", nil)
		w := httptest.NewRecorder()

		handler(w, req)

		assert.Equal(t, http.StatusOK, w.Code)
		assert.Contains(t, w.Body.String(), "🟢")
		assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
		assert.NotEmpty(t, w.Body.String())
	})

	t.Run("Success - works with different HTTP methods", func(t *testing.T) {
		handler := HealthCheckHandler()
		methods := []string{"GET", "POST", "PUT"}

		for _, method := range methods {
			req := httptest.NewRequest(method, "/health-check", nil)
			w := httptest.NewRecorder()

			handler(w, req)

			assert.Equal(t, http.StatusOK, w.Code)
			assert.Contains(t, w.Body.String(), "🟢")
		}
	})
}
