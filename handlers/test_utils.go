package handlers

import (
	"context"
	"net/http"

	"api.appio.so/middlewares"
	"api.appio.so/models"
	"github.com/appio-so/go-appioid"
)

// AddServiceIDToContext adds a service ID to the request context
func AddServiceIDToContext(req *http.Request, svcID *appioid.ID) *http.Request {
	ctx := context.WithValue(req.Context(), middlewares.SvcIDKey{}, svcID)
	return req.WithContext(ctx)
}

// AddDeviceIDToContext adds a device ID to the request context
func AddDeviceIDToContext(req *http.Request, dvcID *appioid.ID) *http.Request {
	ctx := context.WithValue(req.Context(), middlewares.DvcIDKey{}, dvcID)
	return req.WithContext(ctx)
}

// AddPlatformToContext adds a platform to the request context
func AddPlatformToContext(req *http.Request, platform models.Platform) *http.Request {
	ctx := context.WithValue(req.Context(), middlewares.PlatformKey{}, platform)
	return req.WithContext(ctx)
}
