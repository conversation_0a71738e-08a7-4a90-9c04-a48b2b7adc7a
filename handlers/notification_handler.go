package handlers

import (
	"net/http"

	"api.appio.so/helpers"
	"api.appio.so/models"
	"api.appio.so/pkg"
	"api.appio.so/services"
	"go.uber.org/zap"
)

type NotificationHandler struct {
	Handler

	service services.NotificationServiceInterface
}

func NewNotificationHandler(notificationService services.NotificationServiceInterface, logger *zap.Logger) *NotificationHandler {
	return &NotificationHandler{
		Handler: Handler{logger: logger},
		service: notificationService,
	}
}

func (h *NotificationHandler) Get(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	ntfID, ok := h.GetUrlID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	notification, err := h.service.FindByID(ctx, svcID, ntfID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	if notification == nil {
		helpers.RenderJSONError(w, r, pkg.ErrNotFound)
	} else {
		helpers.RenderJSON(w, r, notification, http.StatusOK)
	}
}

func (h *NotificationHandler) ListDeliveredByDevice(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	dvcID, ok := h.GetDeviceID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()
	notifications, err := h.service.ListDeliveredByDevice(ctx, svcID, dvcID)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	helpers.RenderJSON(w, r, notifications, http.StatusOK)
}

func (h *NotificationHandler) List(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	ctx := r.Context()

	dvcID, _ := h.GetQueryID(r, "device_id")
	status := r.URL.Query().Get("status")
	customerUserID := r.URL.Query().Get("user_id")

	if dvcID != nil {
		notifications, err := h.service.ListAllByDevice(ctx, svcID, dvcID, status)
		if err != nil {
			helpers.RenderJSONError(w, r, err)
			return
		}
		helpers.RenderJSON(w, r, notifications, http.StatusOK)
		return
	}

	if customerUserID != "" {
		notifications, err := h.service.ListAllByCustomerUserID(ctx, svcID, customerUserID, status)
		if err != nil {
			helpers.RenderJSONError(w, r, err)
			return
		}
		helpers.RenderJSON(w, r, notifications, http.StatusOK)
		return
	}

	notifications, err := h.service.List(ctx, svcID, status)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	helpers.RenderJSON(w, r, notifications, http.StatusOK)
}

func (h *NotificationHandler) Create(w http.ResponseWriter, r *http.Request) {
	svcID, ok := h.GetServiceID(w, r)
	if !ok {
		return
	}

	ntfReq, err := helpers.DecodeJSON[models.NotificationRequest](r)
	if err != nil {
		helpers.RenderJSONError(w, r, pkg.ErrInvalidInput)
		return
	}

	if verr := ntfReq.Validate(); verr != nil {
		h.logger.Debug("invalid notification create request", zap.Any("data", ntfReq), zap.Error(verr))
		helpers.RenderJSONError(w, r, pkg.NewUserFacingError(pkg.ErrInvalidInput, verr.Data()))
		return
	}

	// Filters
	dvcID, _ := h.GetQueryID(r, "device_id")
	customerUserID := r.URL.Query().Get("user_id")

	ctx := r.Context()
	ntfID, err := h.service.CreateForeground(ctx, svcID, dvcID, customerUserID, ntfReq)
	if err != nil {
		helpers.RenderJSONError(w, r, err)
		return
	}
	helpers.RenderJSON(w, r, &models.ResponseID{ID: ntfID}, http.StatusCreated)
}
