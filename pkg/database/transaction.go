package database

import (
	"context"
	"fmt"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"go.uber.org/zap"
)

type TxExecutorFunc func(ctx context.Context, pool *pgxpool.Pool, logger *zap.Logger, fn func(tx pgx.Tx) error) error

func ExecuteTx(ctx context.Context, db *pgxpool.Pool, logger *zap.Logger, fn func(pgx.Tx) error) error {
	tx, err := db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("beginning transaction: %w", err)
	}

	defer func(err *error) {
		if *err != nil {
			logger.Debug("rolling back transaction")
			if rbErr := tx.Rollback(context.Background()); rbErr != nil {
				logger.Error("rolling back transaction", zap.Error(rbErr))
			}
		}
	}(&err)

	if err = fn(tx); err != nil {
		return fmt.Errorf("executing transaction: %w", err)
	}

	if err = tx.Commit(ctx); err != nil {
		return fmt.Errorf("committing transaction: %w", err)
	}

	return nil
}
