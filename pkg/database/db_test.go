package database

import (
	"api.appio.so/pkg/config"
	"context"
	"github.com/jackc/pgx/v5"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap/zaptest"
	"testing"
)

func TestPGXQueryTracer(t *testing.T) {
	logger := zaptest.NewLogger(t)
	tracer := &PGXQueryTracer{logger: logger}

	t.Run("TraceQueryStart", func(t *testing.T) {
		ctx := context.Background()
		data := pgx.TraceQueryStartData{
			SQL:  "SELECT * FROM users WHERE id = $1",
			Args: []any{123},
		}

		// Should not panic and should return context
		resultCtx := tracer.TraceQueryStart(ctx, nil, data)
		assert.NotNil(t, resultCtx)
		assert.Equal(t, ctx, resultCtx)
	})

	t.Run("TraceQueryEnd success", func(t *testing.T) {
		ctx := context.Background()
		data := pgx.TraceQueryEndData{
			Err: nil,
		}

		// Should not panic
		tracer.TraceQueryEnd(ctx, nil, data)
	})

	t.Run("TraceQueryEnd with error", func(t *testing.T) {
		ctx := context.Background()
		data := pgx.TraceQueryEndData{
			Err: assert.AnError,
		}

		// Should not panic
		tracer.TraceQueryEnd(ctx, nil, data)
	})

	t.Run("TraceQueryStart with empty SQL", func(t *testing.T) {
		ctx := context.Background()
		data := pgx.TraceQueryStartData{
			SQL:  "",
			Args: nil,
		}

		resultCtx := tracer.TraceQueryStart(ctx, nil, data)
		assert.NotNil(t, resultCtx)
	})

	t.Run("TraceQueryStart with nil args", func(t *testing.T) {
		ctx := context.Background()
		data := pgx.TraceQueryStartData{
			SQL:  "SELECT 1",
			Args: nil,
		}

		resultCtx := tracer.TraceQueryStart(ctx, nil, data)
		assert.NotNil(t, resultCtx)
	})

	t.Run("TraceQueryStart with multiple args", func(t *testing.T) {
		ctx := context.Background()
		data := pgx.TraceQueryStartData{
			SQL:  "INSERT INTO users (name, email, age) VALUES ($1, $2, $3)",
			Args: []any{"John Doe", "<EMAIL>", 30},
		}

		resultCtx := tracer.TraceQueryStart(ctx, nil, data)
		assert.NotNil(t, resultCtx)
	})
}

func TestInitDatabase(t *testing.T) {
	logger := zaptest.NewLogger(t)
	ctx := context.Background()

	t.Run("Empty config returns nil pools", func(t *testing.T) {
		cfg := &config.DBConfig{
			Source:     "",
			SourceFing: "",
			LogQueries: false,
		}

		appioPool, fingPool, err := InitDatabase(ctx, cfg, logger)
		assert.NoError(t, err)
		assert.Nil(t, appioPool)
		assert.Nil(t, fingPool)
	})

	t.Run("Invalid appio connection string", func(t *testing.T) {
		cfg := &config.DBConfig{
			Source:     "invalid-connection-string",
			SourceFing: "",
			LogQueries: false,
		}

		appioPool, fingPool, err := InitDatabase(ctx, cfg, logger)
		assert.Error(t, err)
		assert.Nil(t, appioPool)
		assert.Nil(t, fingPool)
		assert.Contains(t, err.Error(), "parsing appio db config")
	})

	t.Run("Invalid fingerprint connection string", func(t *testing.T) {
		cfg := &config.DBConfig{
			Source:     "",
			SourceFing: "invalid-connection-string",
			LogQueries: false,
		}

		appioPool, fingPool, err := InitDatabase(ctx, cfg, logger)
		assert.Error(t, err)
		assert.Nil(t, appioPool)
		assert.Nil(t, fingPool)
		assert.Contains(t, err.Error(), "parsing fingerprint db config")
	})

	t.Run("Valid connection string format but unreachable", func(t *testing.T) {
		cfg := &config.DBConfig{
			Source:     "postgres://user:pass@localhost:9999/nonexistent",
			SourceFing: "",
			LogQueries: false,
		}

		appioPool, fingPool, err := InitDatabase(ctx, cfg, logger)
		assert.Error(t, err)
		assert.Nil(t, appioPool)
		assert.Nil(t, fingPool)
		// Should fail at connection or ping stage
	})

	t.Run("LogQueries enabled with invalid connection", func(t *testing.T) {
		cfg := &config.DBConfig{
			Source:     "postgres://user:pass@localhost:9999/nonexistent",
			SourceFing: "",
			LogQueries: true, // Enable query logging
		}

		appioPool, fingPool, err := InitDatabase(ctx, cfg, logger)
		assert.Error(t, err)
		assert.Nil(t, appioPool)
		assert.Nil(t, fingPool)
	})

	t.Run("Both databases with invalid connections", func(t *testing.T) {
		cfg := &config.DBConfig{
			Source:     "postgres://user:pass@localhost:9999/appio",
			SourceFing: "postgres://user:pass@localhost:9999/fingerprints",
			LogQueries: true,
		}

		appioPool, fingPool, err := InitDatabase(ctx, cfg, logger)
		assert.Error(t, err)
		assert.Nil(t, appioPool)
		assert.Nil(t, fingPool)
		// Should fail on the first database (appio)
		assert.Contains(t, err.Error(), "appio")
	})
}

func TestInitDatabase_ConfigVariations(t *testing.T) {
	logger := zaptest.NewLogger(t)
	ctx := context.Background()

	t.Run("Only appio database configured", func(t *testing.T) {
		cfg := &config.DBConfig{
			Source:     "postgres://user:pass@localhost:9999/appio",
			SourceFing: "", // Empty fingerprint DB
			LogQueries: false,
		}

		appioPool, fingPool, err := InitDatabase(ctx, cfg, logger)
		assert.Error(t, err) // Will fail because connection is invalid
		assert.Nil(t, appioPool)
		assert.Nil(t, fingPool)
	})

	t.Run("Only fingerprint database configured", func(t *testing.T) {
		cfg := &config.DBConfig{
			Source:     "", // Empty appio DB
			SourceFing: "postgres://user:pass@localhost:9999/fingerprints",
			LogQueries: false,
		}

		appioPool, fingPool, err := InitDatabase(ctx, cfg, logger)
		assert.Error(t, err) // Will fail because connection is invalid
		assert.Nil(t, appioPool)
		assert.Nil(t, fingPool)
	})

	t.Run("LogQueries variations", func(t *testing.T) {
		testCases := []bool{true, false}

		for _, logQueries := range testCases {
			t.Run("LogQueries", func(t *testing.T) {
				cfg := &config.DBConfig{
					Source:     "",
					SourceFing: "",
					LogQueries: logQueries,
				}

				appioPool, fingPool, err := InitDatabase(ctx, cfg, logger)
				assert.NoError(t, err) // Should succeed with empty config
				assert.Nil(t, appioPool)
				assert.Nil(t, fingPool)
			})
		}
	})
}

func TestInitDatabase_ErrorMessages(t *testing.T) {
	logger := zaptest.NewLogger(t)
	ctx := context.Background()

	t.Run("Appio config parsing error", func(t *testing.T) {
		cfg := &config.DBConfig{
			Source:     "not-a-valid-url",
			SourceFing: "",
			LogQueries: false,
		}

		_, _, err := InitDatabase(ctx, cfg, logger)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "parsing appio db config")
	})

	t.Run("Fingerprint config parsing error", func(t *testing.T) {
		cfg := &config.DBConfig{
			Source:     "",
			SourceFing: "not-a-valid-url",
			LogQueries: false,
		}

		_, _, err := InitDatabase(ctx, cfg, logger)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "parsing fingerprint db config")
	})
}
