package pkg

import (
	"api.appio.so/pkg/config"
	"github.com/getsentry/sentry-go"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"go.uber.org/zap/zaptest"
	"testing"
)

func TestNewSentryLogger(t *testing.T) {
	t.Run("Create Sentry logger with valid config", func(t *testing.T) {
		logger := zaptest.NewLogger(t)
		serverCfg := &config.ServerConfig{
			Env:  "test",
			Name: "test-app",
		}
		sentryCfg := &config.SentryConfig{
			DSN:              "", // Empty DSN for testing (won't actually send to Sentry)
			SendDefaultPII:   false,
			TraceSampleRate:  0.1,
		}

		// This should not panic and should return a logger
		sentryLogger := NewSentryLogger(logger, serverCfg, sentryCfg)
		assert.NotNil(t, sentryLogger)
	})

	t.Run("Create Sentry logger with production config", func(t *testing.T) {
		logger := zaptest.NewLogger(t)
		serverCfg := &config.ServerConfig{
			Env:  "production",
			Name: "api-service",
		}
		sentryCfg := &config.SentryConfig{
			DSN:              "", // Empty DSN for testing
			SendDefaultPII:   true,
			TraceSampleRate:  1.0,
		}

		sentryLogger := NewSentryLogger(logger, serverCfg, sentryCfg)
		assert.NotNil(t, sentryLogger)
	})

	t.Run("Create Sentry logger with minimal config", func(t *testing.T) {
		logger := zaptest.NewLogger(t)
		serverCfg := &config.ServerConfig{
			Env:  "",
			Name: "",
		}
		sentryCfg := &config.SentryConfig{
			DSN:              "",
			SendDefaultPII:   false,
			TraceSampleRate:  0.0,
		}

		sentryLogger := NewSentryLogger(logger, serverCfg, sentryCfg)
		assert.NotNil(t, sentryLogger)
	})
}

func TestWithSentryLogger(t *testing.T) {
	t.Run("Attach Sentry core to logger", func(t *testing.T) {
		logger := zaptest.NewLogger(t)
		
		// Create a Sentry client with minimal config for testing
		client, err := sentry.NewClient(sentry.ClientOptions{
			Dsn: "", // Empty DSN for testing
		})
		assert.NoError(t, err)
		assert.NotNil(t, client)

		sentryLogger := withSentryLogger(logger, client)
		assert.NotNil(t, sentryLogger)
		
		// The returned logger should be different from the original
		// (it has the Sentry core attached)
		assert.NotEqual(t, logger, sentryLogger)
	})

	t.Run("Attach Sentry core with nil client should not panic", func(t *testing.T) {
		logger := zaptest.NewLogger(t)
		
		// This should not panic even with nil client
		// The zapsentry library should handle this gracefully
		defer func() {
			if r := recover(); r != nil {
				// If it panics, that's expected behavior for nil client
				t.Logf("Expected panic with nil client: %v", r)
			}
		}()

		sentryLogger := withSentryLogger(logger, nil)
		// If we reach here, the function handled nil client gracefully
		assert.NotNil(t, sentryLogger)
	})
}

func TestSentryIntegration(t *testing.T) {
	t.Run("Sentry logger can log messages", func(t *testing.T) {
		logger := zaptest.NewLogger(t)
		serverCfg := &config.ServerConfig{
			Env:  "test",
			Name: "test-integration",
		}
		sentryCfg := &config.SentryConfig{
			DSN:              "", // Empty DSN for testing
			SendDefaultPII:   false,
			TraceSampleRate:  0.0,
		}

		sentryLogger := NewSentryLogger(logger, serverCfg, sentryCfg)
		
		// These should not panic
		sentryLogger.Info("Test info message")
		sentryLogger.Warn("Test warning message")
		sentryLogger.Error("Test error message")
	})

	t.Run("Sentry logger with different environments", func(t *testing.T) {
		environments := []string{"development", "staging", "production", "test"}
		
		for _, env := range environments {
			t.Run("Environment: "+env, func(t *testing.T) {
				logger := zaptest.NewLogger(t)
				serverCfg := &config.ServerConfig{
					Env:  env,
					Name: "test-app",
				}
				sentryCfg := &config.SentryConfig{
					DSN:              "",
					SendDefaultPII:   false,
					TraceSampleRate:  0.1,
				}

				sentryLogger := NewSentryLogger(logger, serverCfg, sentryCfg)
				assert.NotNil(t, sentryLogger)
				
				// Test logging in different environments
				sentryLogger.Info("Test message", zap.String("env", env))
			})
		}
	})
}

func TestSentryConfiguration(t *testing.T) {
	t.Run("Different trace sample rates", func(t *testing.T) {
		logger := zaptest.NewLogger(t)
		serverCfg := &config.ServerConfig{
			Env:  "test",
			Name: "trace-test",
		}

		sampleRates := []float64{0.0, 0.1, 0.5, 1.0}
		
		for _, rate := range sampleRates {
			t.Run("Sample rate", func(t *testing.T) {
				sentryCfg := &config.SentryConfig{
					DSN:              "",
					SendDefaultPII:   false,
					TraceSampleRate:  rate,
				}

				sentryLogger := NewSentryLogger(logger, serverCfg, sentryCfg)
				assert.NotNil(t, sentryLogger)
			})
		}
	})

	t.Run("SendDefaultPII variations", func(t *testing.T) {
		logger := zaptest.NewLogger(t)
		serverCfg := &config.ServerConfig{
			Env:  "test",
			Name: "pii-test",
		}

		piiSettings := []bool{true, false}
		
		for _, sendPII := range piiSettings {
			t.Run("SendDefaultPII", func(t *testing.T) {
				sentryCfg := &config.SentryConfig{
					DSN:              "",
					SendDefaultPII:   sendPII,
					TraceSampleRate:  0.1,
				}

				sentryLogger := NewSentryLogger(logger, serverCfg, sentryCfg)
				assert.NotNil(t, sentryLogger)
			})
		}
	})
}
