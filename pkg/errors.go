package pkg

import (
	"errors"
	"fmt"
	"net/http"
)

var (
	ErrInvalidInput    = errors.New("invalid input")
	ErrUnauthorized    = errors.New("unauthorized")
	ErrForbidden       = errors.New("forbidden")
	ErrNotFound        = errors.New("not found")
	ErrInternal        = errors.New("internal error")
	ErrMatch           = errors.New("mismatched")
	ErrUnknown         = errors.New("unknown")
	ErrTooManyAttempts = errors.New("too many attempts")
)

func MapErrorToResponse(err error) (int, string) {
	switch {
	case errors.Is(err, ErrInvalidInput):
		return http.StatusBadRequest, "Invalid input data"
	case errors.Is(err, ErrUnauthorized):
		return http.StatusUnauthorized, "Authentication required"
	case errors.Is(err, ErrForbidden):
		return http.StatusForbidden, "Operation not permitted"
	case errors.Is(err, ErrNotFound):
		return http.StatusNotFound, "Resource not found"
	case errors.Is(err, ErrMatch):
		return http.StatusConflict, "Resource conflict"
	case errors.Is(err, ErrTooManyAttempts):
		return http.StatusTooManyRequests, "Too many requests"
	case errors.Is(err, ErrInternal), errors.Is(err, ErrUnknown):
		fallthrough
	default:
		return http.StatusInternalServerError, "An unexpected error occurred"
	}
}

type UserFacingError struct {
	Err     error
	DataMap map[string]any
}

func (e *UserFacingError) Error() string {
	return e.Err.Error()
}

func (e *UserFacingError) Data() map[string]any {
	return e.DataMap
}

func (e *UserFacingError) Unwrap() error {
	return e.Err
}

func NewUserFacingError(err error, data map[string]any) *UserFacingError {
	return &UserFacingError{Err: err, DataMap: data}
}

type ValidationError struct {
	Entity string
	DocUrl string
	Fields []ValidationField
}

// Data are exposed to the user
type ValidationField struct {
	Field  string `json:"field"`
	Reason string `json:"reason"`
}

func (v *ValidationError) Error() string {
	return fmt.Sprintf("validation failed for: %s", v.Entity)
}

func (v *ValidationError) Data() map[string]any {
	return map[string]any{
		"doc_url":           v.DocUrl,
		"entity":            v.Entity,
		"validation_errors": v.Fields,
	}
}
