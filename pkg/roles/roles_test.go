package roles

import (
	"github.com/stretchr/testify/assert"
	"testing"
)

func TestRoleConstants(t *testing.T) {
	t.Run("Role constants are defined correctly", func(t *testing.T) {
		assert.Equal(t, Role(""), Unknown)
		assert.Equal(t, Role("api"), Api)
		assert.Equal(t, Role("api-demo"), ApiDemo)
		assert.Equal(t, Role("app.appio.so"), AppAppioSo)
		assert.Equal(t, Role("demo.appio.so"), DemoAppioSo)
		assert.Equal(t, Role("ios"), IOS)
	})

	t.Run("Role constants are unique", func(t *testing.T) {
		roles := []Role{Unknown, Api, ApiDemo, AppAppioSo, DemoAppioSo, IOS}

		// Check that all roles are different (except Unknown which is empty)
		for i, role1 := range roles {
			for j, role2 := range roles {
				if i != j && role1 != Unknown && role2 != Unknown {
					assert.NotEqual(t, role1, role2, "Roles should be unique: %s vs %s", role1, role2)
				}
			}
		}
	})

	t.Run("Role string values", func(t *testing.T) {
		assert.Equal(t, "", string(Unknown))
		assert.Equal(t, "api", string(Api))
		assert.Equal(t, "api-demo", string(ApiDemo))
		assert.Equal(t, "app.appio.so", string(AppAppioSo))
		assert.Equal(t, "demo.appio.so", string(DemoAppioSo))
		assert.Equal(t, "ios", string(IOS))
	})
}

func TestRoleType(t *testing.T) {
	t.Run("Role type is string", func(t *testing.T) {
		var role Role = "test"
		assert.IsType(t, Role(""), role)
		assert.Equal(t, "test", string(role))
	})

	t.Run("Role can be created from string", func(t *testing.T) {
		roleStr := "custom-role"
		role := Role(roleStr)
		assert.Equal(t, roleStr, string(role))
	})

	t.Run("Role comparison", func(t *testing.T) {
		role1 := Role("test")
		role2 := Role("test")
		role3 := Role("different")

		assert.Equal(t, role1, role2)
		assert.NotEqual(t, role1, role3)
	})

	t.Run("Empty role", func(t *testing.T) {
		var role Role
		assert.Equal(t, Unknown, role)
		assert.Equal(t, "", string(role))
	})
}

func TestRoleUsage(t *testing.T) {
	t.Run("Role in slice", func(t *testing.T) {
		allowedRoles := []Role{Api, ApiDemo, IOS}

		assert.Contains(t, allowedRoles, Api)
		assert.Contains(t, allowedRoles, ApiDemo)
		assert.Contains(t, allowedRoles, IOS)
		assert.NotContains(t, allowedRoles, AppAppioSo)
		assert.NotContains(t, allowedRoles, Unknown)
	})

	t.Run("Role in map", func(t *testing.T) {
		rolePermissions := map[Role][]string{
			Api:         {"read", "write"},
			ApiDemo:     {"read"},
			IOS:         {"read", "device"},
			AppAppioSo:  {"admin"},
			DemoAppioSo: {"demo"},
		}

		assert.Equal(t, []string{"read", "write"}, rolePermissions[Api])
		assert.Equal(t, []string{"read"}, rolePermissions[ApiDemo])
		assert.Equal(t, []string{"read", "device"}, rolePermissions[IOS])
		assert.Equal(t, []string{"admin"}, rolePermissions[AppAppioSo])
		assert.Equal(t, []string{"demo"}, rolePermissions[DemoAppioSo])
		assert.Nil(t, rolePermissions[Unknown])
	})

	t.Run("Role validation function", func(t *testing.T) {
		validRoles := []Role{Api, ApiDemo, AppAppioSo, DemoAppioSo, IOS}

		isValidRole := func(role Role) bool {
			for _, validRole := range validRoles {
				if role == validRole {
					return true
				}
			}
			return false
		}

		assert.True(t, isValidRole(Api))
		assert.True(t, isValidRole(ApiDemo))
		assert.True(t, isValidRole(AppAppioSo))
		assert.True(t, isValidRole(DemoAppioSo))
		assert.True(t, isValidRole(IOS))
		assert.False(t, isValidRole(Unknown))
		assert.False(t, isValidRole(Role("invalid")))
	})
}

func TestRoleStringConversion(t *testing.T) {
	t.Run("Convert string to role", func(t *testing.T) {
		testCases := map[string]Role{
			"":              Unknown,
			"api":           Api,
			"api-demo":      ApiDemo,
			"app.appio.so":  AppAppioSo,
			"demo.appio.so": DemoAppioSo,
			"ios":           IOS,
		}

		for str, expectedRole := range testCases {
			role := Role(str)
			assert.Equal(t, expectedRole, role)
		}
	})

	t.Run("Convert role to string", func(t *testing.T) {
		testCases := map[Role]string{
			Unknown:     "",
			Api:         "api",
			ApiDemo:     "api-demo",
			AppAppioSo:  "app.appio.so",
			DemoAppioSo: "demo.appio.so",
			IOS:         "ios",
		}

		for role, expectedStr := range testCases {
			str := string(role)
			assert.Equal(t, expectedStr, str)
		}
	})
}

func TestRoleEdgeCases(t *testing.T) {
	t.Run("Case sensitivity", func(t *testing.T) {
		assert.NotEqual(t, Api, Role("API"))
		assert.NotEqual(t, Api, Role("Api"))
		assert.Equal(t, IOS, Role("ios")) // IOS constant is actually "ios"
		assert.NotEqual(t, IOS, Role("iOS"))
	})

	t.Run("Whitespace handling", func(t *testing.T) {
		assert.NotEqual(t, Api, Role(" api"))
		assert.NotEqual(t, Api, Role("api "))
		assert.NotEqual(t, Api, Role(" api "))
	})

	t.Run("Special characters", func(t *testing.T) {
		specialRole := Role("role-with-special.chars@domain")
		assert.Equal(t, "role-with-special.chars@domain", string(specialRole))
	})

	t.Run("Unicode characters", func(t *testing.T) {
		unicodeRole := Role("роль")
		assert.Equal(t, "роль", string(unicodeRole))
	})
}
