package pkg

import (
	"errors"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestMapErrorToResponse(t *testing.T) {
	tests := []struct {
		name           string
		err            error
		expectedStatus int
		expectedMsg    string
	}{
		{
			name:           "ErrInvalidInput",
			err:            ErrInvalidInput,
			expectedStatus: http.StatusBadRequest,
			expectedMsg:    "Invalid input data",
		},
		{
			name:           "ErrUnauthorized",
			err:            ErrUnauthorized,
			expectedStatus: http.StatusUnauthorized,
			expectedMsg:    "Authentication required",
		},
		{
			name:           "ErrForbidden",
			err:            ErrForbidden,
			expectedStatus: http.StatusForbidden,
			expectedMsg:    "Operation not permitted",
		},
		{
			name:           "ErrNotFound",
			err:            ErrNotFound,
			expectedStatus: http.StatusNotFound,
			expectedMsg:    "Resource not found",
		},
		{
			name:           "ErrMatch",
			err:            ErrMatch,
			expectedStatus: http.StatusConflict,
			expectedMsg:    "Resource conflict",
		},
		{
			name:           "ErrTooManyAttempts",
			err:            ErrTooManyAttempts,
			expectedStatus: http.StatusTooManyRequests,
			expectedMsg:    "Too many requests",
		},
		{
			name:           "ErrInternal",
			err:            ErrInternal,
			expectedStatus: http.StatusInternalServerError,
			expectedMsg:    "An unexpected error occurred",
		},
		{
			name:           "ErrUnknown",
			err:            ErrUnknown,
			expectedStatus: http.StatusInternalServerError,
			expectedMsg:    "An unexpected error occurred",
		},
		{
			name:           "Custom error",
			err:            errors.New("custom error"),
			expectedStatus: http.StatusInternalServerError,
			expectedMsg:    "An unexpected error occurred",
		},
		{
			name:           "Nil error",
			err:            nil,
			expectedStatus: http.StatusInternalServerError,
			expectedMsg:    "An unexpected error occurred",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			status, msg := MapErrorToResponse(tt.err)
			assert.Equal(t, tt.expectedStatus, status)
			assert.Equal(t, tt.expectedMsg, msg)
		})
	}
}

func TestMapErrorToResponse_WrappedErrors(t *testing.T) {
	t.Run("Wrapped ErrInvalidInput", func(t *testing.T) {
		wrappedErr := errors.Join(ErrInvalidInput, errors.New("additional context"))
		status, msg := MapErrorToResponse(wrappedErr)
		assert.Equal(t, http.StatusBadRequest, status)
		assert.Equal(t, "Invalid input data", msg)
	})

	t.Run("Wrapped ErrNotFound", func(t *testing.T) {
		wrappedErr := errors.Join(ErrNotFound, errors.New("resource xyz"))
		status, msg := MapErrorToResponse(wrappedErr)
		assert.Equal(t, http.StatusNotFound, status)
		assert.Equal(t, "Resource not found", msg)
	})
}

func TestUserFacingError(t *testing.T) {
	t.Run("Create UserFacingError", func(t *testing.T) {
		baseErr := ErrInvalidInput
		data := map[string]any{
			"field": "email",
			"value": "invalid-email",
		}

		userErr := NewUserFacingError(baseErr, data)

		assert.NotNil(t, userErr)
		assert.Equal(t, baseErr.Error(), userErr.Error())
		assert.Equal(t, data, userErr.Data())
		assert.Equal(t, baseErr, userErr.Unwrap())
	})

	t.Run("UserFacingError with nil data", func(t *testing.T) {
		baseErr := ErrNotFound
		userErr := NewUserFacingError(baseErr, nil)

		assert.NotNil(t, userErr)
		assert.Equal(t, baseErr.Error(), userErr.Error())
		assert.Nil(t, userErr.Data())
		assert.Equal(t, baseErr, userErr.Unwrap())
	})

	t.Run("UserFacingError with empty data", func(t *testing.T) {
		baseErr := ErrForbidden
		data := map[string]any{}
		userErr := NewUserFacingError(baseErr, data)

		assert.NotNil(t, userErr)
		assert.Equal(t, baseErr.Error(), userErr.Error())
		assert.Equal(t, data, userErr.Data())
		assert.Equal(t, baseErr, userErr.Unwrap())
	})
}

func TestValidationError(t *testing.T) {
	t.Run("Create ValidationError", func(t *testing.T) {
		fields := []ValidationField{
			{Field: "email", Reason: "invalid format"},
			{Field: "password", Reason: "too short"},
		}

		validationErr := &ValidationError{
			Entity: "user",
			DocUrl: "https://docs.example.com/user",
			Fields: fields,
		}

		assert.Equal(t, "validation failed for: user", validationErr.Error())

		data := validationErr.Data()
		assert.Equal(t, "https://docs.example.com/user", data["doc_url"])
		assert.Equal(t, "user", data["entity"])
		assert.Equal(t, fields, data["validation_errors"])
	})

	t.Run("ValidationError with empty fields", func(t *testing.T) {
		validationErr := &ValidationError{
			Entity: "product",
			DocUrl: "https://docs.example.com/product",
			Fields: []ValidationField{},
		}

		assert.Equal(t, "validation failed for: product", validationErr.Error())

		data := validationErr.Data()
		assert.Equal(t, "https://docs.example.com/product", data["doc_url"])
		assert.Equal(t, "product", data["entity"])
		assert.Empty(t, data["validation_errors"])
	})

	t.Run("ValidationError with nil fields", func(t *testing.T) {
		validationErr := &ValidationError{
			Entity: "order",
			DocUrl: "",
			Fields: nil,
		}

		assert.Equal(t, "validation failed for: order", validationErr.Error())

		data := validationErr.Data()
		assert.Equal(t, "", data["doc_url"])
		assert.Equal(t, "order", data["entity"])
		assert.Nil(t, data["validation_errors"])
	})
}

func TestValidationField(t *testing.T) {
	t.Run("ValidationField structure", func(t *testing.T) {
		field := ValidationField{
			Field:  "username",
			Reason: "already exists",
		}

		assert.Equal(t, "username", field.Field)
		assert.Equal(t, "already exists", field.Reason)
	})
}

func TestErrorConstants(t *testing.T) {
	t.Run("RawError constants are defined", func(t *testing.T) {
		assert.NotNil(t, ErrInvalidInput)
		assert.NotNil(t, ErrUnauthorized)
		assert.NotNil(t, ErrForbidden)
		assert.NotNil(t, ErrNotFound)
		assert.NotNil(t, ErrInternal)
		assert.NotNil(t, ErrMatch)
		assert.NotNil(t, ErrUnknown)
		assert.NotNil(t, ErrTooManyAttempts)
	})

	t.Run("RawError messages are correct", func(t *testing.T) {
		assert.Equal(t, "invalid input", ErrInvalidInput.Error())
		assert.Equal(t, "unauthorized", ErrUnauthorized.Error())
		assert.Equal(t, "forbidden", ErrForbidden.Error())
		assert.Equal(t, "not found", ErrNotFound.Error())
		assert.Equal(t, "internal error", ErrInternal.Error())
		assert.Equal(t, "mismatched", ErrMatch.Error())
		assert.Equal(t, "unknown", ErrUnknown.Error())
		assert.Equal(t, "too many attempts", ErrTooManyAttempts.Error())
	})
}
