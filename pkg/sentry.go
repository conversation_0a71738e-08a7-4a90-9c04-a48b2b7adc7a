package pkg

import (
	"api.appio.so/pkg/config"
	"fmt"
	"github.com/TheZeroSlave/zapsentry"
	"github.com/getsentry/sentry-go"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"time"
)

func NewSentryLogger(logger *zap.Logger, serverCfg *config.ServerConfig, sentryCfg *config.SentryConfig) *zap.Logger {
	sentryClient, err := sentry.NewClient(sentry.ClientOptions{
		Environment:      serverCfg.Env,
		Dsn:              sentryCfg.DSN,
		SendDefaultPII:   sentryCfg.SendDefaultPII,
		Release:          fmt.Sprintf("%s@%s", serverCfg.Name, serverCfg.Env),
		TracesSampleRate: sentryCfg.TraceSampleRate,
	})
	if err != nil {
		logger.Fatal("failed to init sentry", zap.Error(err))
	}
	defer sentry.Flush(2 * time.Second)

	// link sentry with logger
	return withSentryLogger(logger, sentryClient)
}

func withSentryLogger(logger *zap.Logger, client *sentry.Client) *zap.Logger {
	cfg := zapsentry.Configuration{
		Level:             zapcore.ErrorLevel, //when to send a message to sentry
		EnableBreadcrumbs: true,               // enable sending breadcrumbs to Sentry
		BreadcrumbLevel:   zapcore.InfoLevel,  // at what level should we send breadcrumbs to sentry. can't be higher than `Level`
	}
	core, err := zapsentry.NewCore(cfg, zapsentry.NewSentryClientFromClient(client))
	if err != nil {
		logger.Fatal("failed to link Sentry with logger", zap.Error(err))
		panic(err)
	}

	return zapsentry.AttachCoreToLogger(core, logger)
}
