package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestConfig_Validate(t *testing.T) {
	t.Run("Valid config", func(t *testing.T) {
		cfg := &Config{
			Server: ServerConfig{
				Name:     "test-api",
				Env:      "dev",
				Port:     8080,
				LogLevel: "info",
				Timeout:  "30s",
			},
			DB: DBConfig{
				Source:     "postgres://user:pass@localhost:5432/testdb?sslmode=disable",
				SourceFing: "postgres://user:pass@localhost:5432/fingdb?sslmode=disable",
				LogQueries: false,
			},
			Auth: AuthConfig{
				App:     "app_key_12345678901234567890123456789012345678901234567890",
				Demo:    "demo_key_12345678901234567890123456789012345678901234567890",
				IOS:     "ios_key_12345678901234567890123456789012345678901234567890",
				Android: "android_key_12345678901234567890123456789012345678901234567890",
			},
		}

		err := cfg.Validate()
		assert.NoError(t, err)
	})

	t.Run("Invalid server config", func(t *testing.T) {
		cfg := &Config{
			Server: ServerConfig{
				Name:     "", // Invalid: empty name
				Env:      "dev",
				Port:     8080,
				LogLevel: "info",
				Timeout:  "30s",
			},
			DB: DBConfig{
				Source:     "postgres://user:pass@localhost:5432/testdb?sslmode=disable",
				SourceFing: "postgres://user:pass@localhost:5432/fingdb?sslmode=disable",
			},
			Auth: AuthConfig{
				App:     "app_key_12345678901234567890123456789012345678901234567890",
				Demo:    "demo_key_12345678901234567890123456789012345678901234567890",
				IOS:     "ios_key_12345678901234567890123456789012345678901234567890",
				Android: "android_key_12345678901234567890123456789012345678901234567890",
			},
		}

		err := cfg.Validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "server name is required")
	})

	t.Run("Invalid database config", func(t *testing.T) {
		cfg := &Config{
			Server: ServerConfig{
				Name:     "test-api",
				Env:      "dev",
				Port:     8080,
				LogLevel: "info",
				Timeout:  "30s",
			},
			DB: DBConfig{
				Source:     "", // Invalid: empty source
				SourceFing: "postgres://user:pass@localhost:5432/fingdb?sslmode=disable",
			},
			Auth: AuthConfig{
				App:     "app_key_12345678901234567890123456789012345678901234567890",
				Demo:    "demo_key_12345678901234567890123456789012345678901234567890",
				IOS:     "ios_key_12345678901234567890123456789012345678901234567890",
				Android: "android_key_12345678901234567890123456789012345678901234567890",
			},
		}

		err := cfg.Validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "database source is required")
	})

	t.Run("Invalid auth config", func(t *testing.T) {
		cfg := &Config{
			Server: ServerConfig{
				Name:     "test-api",
				Env:      "dev",
				Port:     8080,
				LogLevel: "info",
				Timeout:  "30s",
			},
			DB: DBConfig{
				Source:     "postgres://user:pass@localhost:5432/testdb?sslmode=disable",
				SourceFing: "postgres://user:pass@localhost:5432/fingdb?sslmode=disable",
			},
			Auth: AuthConfig{
				App:     "short", // Invalid: too short
				Demo:    "demo_key_12345678901234567890123456789012345678901234567890",
				IOS:     "ios_key_12345678901234567890123456789012345678901234567890",
				Android: "android_key_12345678901234567890123456789012345678901234567890",
			},
		}

		err := cfg.Validate()
		require.Error(t, err)
		assert.Contains(t, err.Error(), "auth app key must be at least 50 characters long")
	})
}

func TestServerConfig_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      ServerConfig
		expectError string
	}{
		{
			name: "Valid config",
			config: ServerConfig{
				Name:     "test-api",
				Env:      "dev",
				Port:     8080,
				LogLevel: "info",
				Timeout:  "30s",
			},
			expectError: "",
		},
		{
			name: "Invalid environment",
			config: ServerConfig{
				Name:     "test-api",
				Env:      "invalid",
				Port:     8080,
				LogLevel: "info",
				Timeout:  "30s",
			},
			expectError: "server environment must be one of",
		},
		{
			name: "Invalid port",
			config: ServerConfig{
				Name:     "test-api",
				Env:      "dev",
				Port:     -1,
				LogLevel: "info",
				Timeout:  "30s",
			},
			expectError: "server port must be between 1 and 65535",
		},
		{
			name: "Invalid log level",
			config: ServerConfig{
				Name:     "test-api",
				Env:      "dev",
				Port:     8080,
				LogLevel: "invalid",
				Timeout:  "30s",
			},
			expectError: "server log level must be one of",
		},
		{
			name: "Invalid timeout",
			config: ServerConfig{
				Name:     "test-api",
				Env:      "dev",
				Port:     8080,
				LogLevel: "info",
				Timeout:  "invalid",
			},
			expectError: "server timeout must be a valid duration",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError == "" {
				assert.NoError(t, err)
			} else {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectError)
			}
		})
	}
}

func TestDBConfig_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      DBConfig
		expectError string
	}{
		{
			name: "Valid config",
			config: DBConfig{
				Source:     "postgres://user:pass@localhost:5432/testdb?sslmode=disable",
				SourceFing: "postgres://user:pass@localhost:5432/fingdb?sslmode=disable",
			},
			expectError: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError == "" {
				assert.NoError(t, err)
			} else {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectError)
			}
		})
	}
}

func TestAuthConfig_Validate(t *testing.T) {
	tests := []struct {
		name        string
		config      AuthConfig
		expectError string
	}{
		{
			name: "Valid config",
			config: AuthConfig{
				App:     "app_key_12345678901234567890123456789012345678901234567890",
				Demo:    "demo_key_12345678901234567890123456789012345678901234567890",
				IOS:     "ios_key_12345678901234567890123456789012345678901234567890",
				Android: "android_key_12345678901234567890123456789012345678901234567890",
			},
			expectError: "",
		},
		{
			name: "Duplicate keys",
			config: AuthConfig{
				App:     "same_key_12345678901234567890123456789012345678901234567890",
				Demo:    "same_key_12345678901234567890123456789012345678901234567890", // Same as App
				IOS:     "ios_key_12345678901234567890123456789012345678901234567890",
				Android: "android_key_12345678901234567890123456789012345678901234567890",
			},
			expectError: "all auth keys must be unique",
		},
		{
			name: "Short key",
			config: AuthConfig{
				App:     "short",
				Demo:    "demo_key_12345678901234567890123456789012345678901234567890",
				IOS:     "ios_key_12345678901234567890123456789012345678901234567890",
				Android: "android_key_12345678901234567890123456789012345678901234567890",
			},
			expectError: "auth app key must be at least 50 characters long",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()
			if tt.expectError == "" {
				assert.NoError(t, err)
			} else {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectError)
			}
		})
	}
}
